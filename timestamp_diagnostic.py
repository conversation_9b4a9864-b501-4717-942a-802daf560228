#!/usr/bin/env python3
"""
Timestamp Diagnostic Tool for Discord Bot Application System

This script diagnoses timestamp issues in the Discord bot's application system,
specifically investigating the date calculation problem where embeds show 
incorrect relative times.

Issue: Application embeds showing "2 days ago" when dates appear to be August 8th 
vs August 10th, but investigation reveals system clock issues.
"""

import os
import sys
import pymongo
from datetime import datetime, timezone
import json

def check_system_time():
    """Check system time and timezone settings"""
    print("🕐 SYSTEM TIME DIAGNOSTIC")
    print("=" * 50)
    
    # Get various time representations
    local_time = datetime.now()
    utc_time = datetime.now(timezone.utc)
    local_with_tz = datetime.now().astimezone()
    
    print(f"Local time (naive):     {local_time}")
    print(f"UTC time:               {utc_time}")
    print(f"Local time (with TZ):   {local_with_tz}")
    print(f"Local timezone:         {local_with_tz.tzinfo}")
    print(f"UTC offset:             {local_with_tz.utcoffset()}")
    
    # Check if year is correct
    expected_year = 2024  # Update this as needed
    if local_time.year != expected_year:
        print(f"⚠️  WARNING: System year is {local_time.year}, expected {expected_year}")
        print("   This could cause timestamp calculation issues!")
    else:
        print(f"✅ System year is correct: {local_time.year}")
    
    return {
        'local_time': local_time,
        'utc_time': utc_time,
        'local_with_tz': local_with_tz,
        'year_correct': local_time.year == expected_year
    }

def check_database_timestamps():
    """Check timestamps stored in the MongoDB database"""
    print("\n🗄️ DATABASE TIMESTAMP DIAGNOSTIC")
    print("=" * 50)
    
    try:
        # Connect to MongoDB
        client = pymongo.MongoClient("mongodb://localhost:27017/")
        db = client["missminutesbot"]
        
        # Check applications collection
        applications_collection = db["applications"]
        app_doc = applications_collection.find_one({"_id": "applications"})
        
        if not app_doc or "status" not in app_doc:
            print("❌ No application data found in database")
            return None
        
        app_data = app_doc["status"]
        print(f"📊 Found {len(app_data)} applications in database")
        
        timestamp_issues = []
        
        for user_id, data in app_data.items():
            print(f"\n👤 User: {user_id}")
            
            # Check various timestamp fields
            timestamp_fields = ["response_time", "submission_time", "processed_time"]
            
            for field in timestamp_fields:
                if field in data:
                    timestamp_str = data[field]
                    print(f"   {field}: {timestamp_str}")
                    
                    try:
                        # Parse the timestamp
                        if timestamp_str.endswith('Z'):
                            parsed_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        elif '+' in timestamp_str:
                            parsed_time = datetime.fromisoformat(timestamp_str)
                        else:
                            parsed_time = datetime.fromisoformat(timestamp_str).replace(tzinfo=timezone.utc)
                        
                        # Check if year is suspicious
                        current_year = datetime.now().year
                        if parsed_time.year != current_year:
                            issue = {
                                'user_id': user_id,
                                'field': field,
                                'timestamp': timestamp_str,
                                'parsed_year': parsed_time.year,
                                'expected_year': current_year,
                                'issue': f"Year mismatch: {parsed_time.year} vs {current_year}"
                            }
                            timestamp_issues.append(issue)
                            print(f"     ⚠️  Year issue: {parsed_time.year} (expected {current_year})")
                        else:
                            print(f"     ✅ Year correct: {parsed_time.year}")
                            
                        # Calculate relative time
                        now = datetime.now(timezone.utc)
                        time_diff = now - parsed_time
                        days_ago = time_diff.days
                        hours_ago = time_diff.seconds // 3600
                        
                        print(f"     📅 Time difference: {days_ago} days, {hours_ago} hours ago")
                        
                    except Exception as e:
                        print(f"     ❌ Parse error: {e}")
        
        return {
            'total_applications': len(app_data),
            'timestamp_issues': timestamp_issues
        }
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return None

def analyze_discord_timestamp_calculation():
    """Analyze how Discord timestamp calculations work"""
    print("\n📅 DISCORD TIMESTAMP ANALYSIS")
    print("=" * 50)
    
    # Test various dates around the reported issue
    test_dates = [
        ("August 8, 2024", datetime(2024, 8, 8, 12, 0, 0, tzinfo=timezone.utc)),
        ("August 9, 2024", datetime(2024, 8, 9, 12, 0, 0, tzinfo=timezone.utc)),
        ("August 10, 2024", datetime(2024, 8, 10, 12, 0, 0, tzinfo=timezone.utc)),
        ("August 8, 2025", datetime(2025, 8, 8, 12, 0, 0, tzinfo=timezone.utc)),
        ("August 9, 2025", datetime(2025, 8, 9, 12, 0, 0, tzinfo=timezone.utc)),
        ("August 10, 2025", datetime(2025, 8, 10, 12, 0, 0, tzinfo=timezone.utc)),
    ]
    
    current_time = datetime.now(timezone.utc)
    print(f"Current time: {current_time}")
    print()
    
    for date_name, test_date in test_dates:
        time_diff = current_time - test_date
        days_diff = time_diff.days
        hours_diff = time_diff.seconds // 3600
        
        # Convert to Unix timestamp for Discord
        unix_ts = int(test_date.timestamp())
        
        print(f"{date_name:20} | {test_date} | {days_diff:4d} days ago | <t:{unix_ts}:R>")

def generate_fix_recommendations():
    """Generate recommendations to fix timestamp issues"""
    print("\n🔧 FIX RECOMMENDATIONS")
    print("=" * 50)
    
    system_info = check_system_time()
    
    if not system_info['year_correct']:
        print("🚨 CRITICAL: System clock year is incorrect!")
        print()
        print("Immediate actions needed:")
        print("1. Fix system clock:")
        print("   - Windows: Right-click taskbar clock → 'Adjust date/time'")
        print("   - Linux: sudo timedatectl set-time 'YYYY-MM-DD HH:MM:SS'")
        print("   - Or enable automatic time synchronization")
        print()
        print("2. Update existing database timestamps:")
        print("   - Run timestamp correction script")
        print("   - Or manually update affected records")
        print()
        print("3. Restart Discord bot after fixing system time")
    else:
        print("✅ System clock appears correct")
        print()
        print("Other potential issues to check:")
        print("1. Timezone configuration in bot code")
        print("2. Database timestamp storage format")
        print("3. Discord timestamp parsing logic")

def main():
    """Main diagnostic function"""
    print("🔍 DISCORD BOT TIMESTAMP DIAGNOSTIC TOOL")
    print("=" * 60)
    print("Investigating date calculation issues in application embeds")
    print("=" * 60)
    
    # Run all diagnostic checks
    system_info = check_system_time()
    db_info = check_database_timestamps()
    analyze_discord_timestamp_calculation()
    generate_fix_recommendations()
    
    # Summary
    print("\n📋 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    if system_info and not system_info['year_correct']:
        print("🚨 PRIMARY ISSUE: System clock year is incorrect")
        print("   This is likely the root cause of the timestamp display issues")
    elif db_info and db_info['timestamp_issues']:
        print("⚠️  DATABASE ISSUES: Found timestamp inconsistencies")
        for issue in db_info['timestamp_issues']:
            print(f"   - {issue['issue']} for user {issue['user_id']}")
    else:
        print("✅ No obvious timestamp issues detected")
        print("   The 'X days ago' calculation may be working correctly")
        print("   Double-check the actual dates being compared")

if __name__ == "__main__":
    main()
