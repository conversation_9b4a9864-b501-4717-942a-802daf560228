"""
Test script for Activity Tracker Setup Workflow

This script tests the complete workflow including:
1. Configuration setup through modals
2. Bot restart simulation
3. Dashboard functionality with configured roles
4. Data persistence verification
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_setup_workflow():
    """Test the complete setup workflow"""
    print("🔧 Testing Activity Tracker Setup Workflow")
    print("=" * 50)
    
    try:
        from activity_tracker import activity_tracker, TrackedRolesModal, AccessRolesModal
        
        print("1. Initializing Activity Tracker...")
        await activity_tracker.initialize()
        print("✅ Activity tracker initialized")
        
        print("\n2. Testing Modal Configuration Workflow...")
        
        # Create mock guild and roles
        mock_guild = Mock()
        mock_guild.id = 12345
        mock_guild.name = "Test Guild"
        
        # Create mock roles
        staff_role = Mock()
        staff_role.id = 111111111
        staff_role.name = "Staff"
        staff_role.members = [Mock(bot=False) for _ in range(5)]  # 5 staff members
        
        vip_role = Mock()
        vip_role.id = 222222222
        vip_role.name = "VIP"
        vip_role.members = [Mock(bot=False) for _ in range(10)]  # 10 VIP members
        
        admin_role = Mock()
        admin_role.id = 333333333
        admin_role.name = "Admin"
        admin_role.members = [Mock(bot=False) for _ in range(3)]  # 3 admin members
        
        # Mock guild.get_role method
        def get_role(role_id):
            if role_id == 111111111:
                return staff_role
            elif role_id == 222222222:
                return vip_role
            elif role_id == 333333333:
                return admin_role
            return None
        
        mock_guild.get_role = get_role
        mock_guild.roles = [staff_role, vip_role, admin_role]
        
        # Test direct configuration (simulating modal input)
        print("   Testing role configuration directly...")

        # Simulate TrackedRolesModal workflow
        role_inputs = ["Staff", "222222222"]  # Mix of name and ID
        new_tracked_roles = set()

        for role_input in role_inputs:
            if role_input.isdigit():
                role = mock_guild.get_role(int(role_input))
            else:
                # Find role by name
                role = next((r for r in mock_guild.roles if r.name == role_input), None)

            if role:
                new_tracked_roles.add(role.id)

        activity_tracker.tracked_roles = new_tracked_roles
        save_result = await activity_tracker._save_config()

        if save_result and new_tracked_roles == {111111111, 222222222}:
            print("   ✅ Tracked roles configuration successful")
        else:
            print(f"   ❌ Tracked roles configuration failed. Expected: {{111111111, 222222222}}, Got: {new_tracked_roles}")
            return False

        # Test AccessRolesModal workflow
        print("   Testing access roles configuration...")

        role_inputs = ["333333333"]  # Admin role ID
        new_access_roles = set()

        for role_input in role_inputs:
            if role_input.isdigit():
                role = mock_guild.get_role(int(role_input))
                if role:
                    new_access_roles.add(role.id)

        activity_tracker.authorized_roles = new_access_roles
        save_result = await activity_tracker._save_config()

        if save_result and new_access_roles == {333333333}:
            print("   ✅ Access roles configuration successful")
        else:
            print(f"   ❌ Access roles configuration failed. Expected: {{333333333}}, Got: {new_access_roles}")
            return False
        
        print("\n3. Testing Bot Restart Simulation...")
        
        # Save current configuration
        original_tracked = activity_tracker.tracked_roles.copy()
        original_access = activity_tracker.authorized_roles.copy()
        
        # Simulate bot restart by clearing configuration and reloading
        activity_tracker.tracked_roles = set()
        activity_tracker.authorized_roles = set()
        
        print("   Configuration cleared (simulating restart)")
        print(f"   Tracked roles: {activity_tracker.tracked_roles}")
        print(f"   Access roles: {activity_tracker.authorized_roles}")
        
        # Reload configuration
        await activity_tracker._load_config()
        
        print("   Configuration reloaded from database")
        print(f"   Tracked roles: {activity_tracker.tracked_roles}")
        print(f"   Access roles: {activity_tracker.authorized_roles}")
        
        # Verify configuration persisted
        if (activity_tracker.tracked_roles == original_tracked and 
            activity_tracker.authorized_roles == original_access):
            print("   ✅ Configuration survived restart simulation")
        else:
            print("   ❌ Configuration did not survive restart simulation")
            return False
        
        print("\n4. Testing Dashboard Role Detection...")
        
        # Test role detection for dashboard
        if activity_tracker.tracked_roles:
            print(f"   ✅ Dashboard will show {len(activity_tracker.tracked_roles)} tracked roles")
        else:
            print("   ❌ Dashboard will not show any tracked roles")
            return False
        
        # Test access permission checking
        mock_user = Mock()
        mock_user.roles = [admin_role]  # User has admin role
        
        has_access = await activity_tracker.has_access_permission(mock_user)
        if has_access:
            print("   ✅ Access permission checking works correctly")
        else:
            print("   ❌ Access permission checking failed")
            return False
        
        # Test member tracking detection
        mock_member = Mock()
        mock_member.roles = [staff_role]  # Member has staff role
        
        is_tracked = await activity_tracker.is_member_tracked(mock_member)
        if is_tracked:
            print("   ✅ Member tracking detection works correctly")
        else:
            print("   ❌ Member tracking detection failed")
            return False
        
        print("\n5. Testing Configuration Display...")
        
        from activity_tracker import ActivitySetupView
        setup_view = ActivitySetupView(activity_tracker)
        config_embed = await setup_view._create_config_embed(mock_guild)
        
        if config_embed.title == "🔧 Activity Tracker Configuration":
            print("   ✅ Configuration embed created successfully")
            print(f"   Embed has {len(config_embed.fields)} fields")
            
            # Check for tracked roles field
            tracked_field = next((field for field in config_embed.fields 
                                if "Tracked Roles" in field.name), None)
            if tracked_field and "Staff" in tracked_field.value and "VIP" in tracked_field.value:
                print("   ✅ Tracked roles displayed correctly in embed")
            else:
                print("   ❌ Tracked roles not displayed correctly in embed")
                return False
            
            # Check for access roles field
            access_field = next((field for field in config_embed.fields 
                               if "Access Roles" in field.name), None)
            if access_field and "Admin" in access_field.value:
                print("   ✅ Access roles displayed correctly in embed")
            else:
                print("   ❌ Access roles not displayed correctly in embed")
                return False
        else:
            print("   ❌ Configuration embed creation failed")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 All setup workflow tests passed!")
        print("\nWorkflow Summary:")
        print(f"✅ Tracked Roles: {len(activity_tracker.tracked_roles)} configured")
        print(f"✅ Access Roles: {len(activity_tracker.authorized_roles)} configured")
        print("✅ Configuration persists across restarts")
        print("✅ Dashboard will display configured roles")
        print("✅ Access control is working properly")
        print("✅ Member tracking detection is functional")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Setup workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_error_handling():
    """Test error handling in configuration"""
    print("\n🛡️ Testing Error Handling")
    print("-" * 30)
    
    try:
        from activity_tracker import activity_tracker, TrackedRolesModal
        
        # Test invalid role inputs
        print("1. Testing invalid role inputs...")

        mock_guild = Mock()
        mock_guild.get_role = Mock(return_value=None)  # Always return None
        mock_guild.roles = []

        # Test invalid role processing
        role_inputs = ["NonexistentRole", "999999999", "InvalidID"]
        new_tracked_roles = set()

        for role_input in role_inputs:
            if role_input.isdigit():
                role = mock_guild.get_role(int(role_input))
            else:
                role = next((r for r in mock_guild.roles if r.name == role_input), None)

            if role:
                new_tracked_roles.add(role.id)

        # Should result in empty set since no roles exist
        if len(new_tracked_roles) == 0:
            print("   ✅ Invalid role inputs handled gracefully")
        else:
            print("   ❌ Invalid role inputs not handled correctly")
            return False

        # Test empty input
        print("2. Testing empty input...")
        empty_roles = set()
        activity_tracker.tracked_roles = empty_roles
        save_result = await activity_tracker._save_config()
        
        if save_result:
            print("   ✅ Empty input handled gracefully")
        else:
            print("   ❌ Empty input not handled correctly")
            return False

        return True
        
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Activity Tracker Setup Workflow Test Suite")
    print("=" * 60)
    
    # Run setup workflow tests
    workflow_result = await test_setup_workflow()
    
    if workflow_result:
        # Run error handling tests
        error_result = await test_error_handling()
        
        if error_result:
            print("\n" + "=" * 60)
            print("🎉 ALL SETUP WORKFLOW TESTS PASSED!")
            print("\nThe activity tracker setup system is working correctly:")
            print("✅ Modal configuration saves properly")
            print("✅ Configuration persists across bot restarts")
            print("✅ Dashboard integration works correctly")
            print("✅ Access control functions properly")
            print("✅ Error handling is robust")
            print("\nYou can now use:")
            print("• /activity_setup - to configure tracked and access roles")
            print("• /activity_search - to access the role-based dashboard")
            return True
    
    print("\n" + "=" * 60)
    print("❌ SETUP WORKFLOW TESTS FAILED!")
    print("\nPlease check the error messages above and fix the issues.")
    return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
