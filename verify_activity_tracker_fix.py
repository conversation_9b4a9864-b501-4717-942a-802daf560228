"""
Verification script for Activity Tracker Configuration Persistence Fix

This script verifies that the data persistence issues have been resolved:
1. Configuration saves properly through modals
2. Configuration loads correctly on bot startup
3. Dashboard shows configured roles
4. All functionality works end-to-end
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def verify_configuration_persistence():
    """Verify that configuration persistence is working correctly"""
    print("🔍 Verifying Activity Tracker Configuration Persistence Fix")
    print("=" * 65)
    
    try:
        from activity_tracker import activity_tracker
        
        print("1. Testing Database Connection and Initialization...")
        await activity_tracker.initialize()
        
        # Check database collections
        if activity_tracker.config_collection is not None:
            print("   ✅ Config collection initialized correctly")
        else:
            print("   ❌ Config collection not initialized")
            return False
        
        if activity_tracker.activity_collection is not None:
            print("   ✅ Activity collection initialized correctly")
        else:
            print("   ❌ Activity collection not initialized")
            return False
        
        print("\n2. Testing Configuration Save/Load Cycle...")
        
        # Test 1: Save configuration
        test_tracked_roles = {111111111, 222222222, 333333333}
        test_access_roles = {444444444, 555555555}
        
        activity_tracker.tracked_roles = test_tracked_roles
        activity_tracker.authorized_roles = test_access_roles
        
        save_result = await activity_tracker._save_config()
        if save_result:
            print("   ✅ Configuration saved successfully")
        else:
            print("   ❌ Configuration save failed")
            return False
        
        # Test 2: Verify database document
        config_doc = await activity_tracker.config_collection.find_one({"_id": "activity_config"})
        if config_doc:
            print("   ✅ Configuration document exists in database")
            
            saved_tracked = set(config_doc.get("tracked_roles", []))
            saved_access = set(config_doc.get("authorized_roles", []))
            
            if saved_tracked == test_tracked_roles:
                print("   ✅ Tracked roles saved correctly to database")
            else:
                print(f"   ❌ Tracked roles mismatch. Expected: {test_tracked_roles}, Got: {saved_tracked}")
                return False
            
            if saved_access == test_access_roles:
                print("   ✅ Access roles saved correctly to database")
            else:
                print(f"   ❌ Access roles mismatch. Expected: {test_access_roles}, Got: {saved_access}")
                return False
        else:
            print("   ❌ Configuration document not found in database")
            return False
        
        # Test 3: Clear and reload
        activity_tracker.tracked_roles = set()
        activity_tracker.authorized_roles = set()
        
        await activity_tracker._load_config()
        
        if activity_tracker.tracked_roles == test_tracked_roles:
            print("   ✅ Tracked roles loaded correctly from database")
        else:
            print(f"   ❌ Tracked roles load failed. Expected: {test_tracked_roles}, Got: {activity_tracker.tracked_roles}")
            return False
        
        if activity_tracker.authorized_roles == test_access_roles:
            print("   ✅ Access roles loaded correctly from database")
        else:
            print(f"   ❌ Access roles load failed. Expected: {test_access_roles}, Got: {activity_tracker.authorized_roles}")
            return False
        
        print("\n3. Testing Modal Configuration Workflow...")
        
        # Test role parsing logic (simulating modal input)
        role_inputs = ["123456789", "987654321"]
        parsed_roles = set()
        
        for role_input in role_inputs:
            if role_input.isdigit():
                parsed_roles.add(int(role_input))
        
        activity_tracker.tracked_roles = parsed_roles
        save_result = await activity_tracker._save_config()
        
        if save_result:
            print("   ✅ Modal workflow simulation successful")
        else:
            print("   ❌ Modal workflow simulation failed")
            return False
        
        print("\n4. Testing Error Handling...")
        
        # Test with None values (should be handled gracefully)
        activity_tracker.tracked_roles = None
        activity_tracker.authorized_roles = None
        save_result = await activity_tracker._save_config()

        if save_result:
            print("   ✅ None values handled gracefully (converted to empty sets)")
            # Reset to empty sets for further testing
            activity_tracker.tracked_roles = set()
            activity_tracker.authorized_roles = set()
        else:
            print("   ❌ None values not handled correctly")
            return False
        
        # Test with empty sets
        save_result = await activity_tracker._save_config()
        if save_result:
            print("   ✅ Empty configuration saves correctly")
        else:
            print("   ❌ Empty configuration save failed")
            return False
        
        print("\n5. Testing Dashboard Integration...")
        
        # Set up test configuration for dashboard
        activity_tracker.tracked_roles = {111111111, 222222222}
        activity_tracker.authorized_roles = {333333333}
        await activity_tracker._save_config()
        
        # Test dashboard role detection
        if len(activity_tracker.tracked_roles) > 0:
            print("   ✅ Dashboard will detect tracked roles")
        else:
            print("   ❌ Dashboard will not detect tracked roles")
            return False
        
        # Test access permission logic
        from unittest.mock import Mock
        
        mock_user = Mock()
        mock_user.roles = [Mock(id=333333333)]  # User has access role
        
        has_access = await activity_tracker.has_access_permission(mock_user)
        if has_access:
            print("   ✅ Access permission checking works")
        else:
            print("   ❌ Access permission checking failed")
            return False
        
        # Test member tracking logic
        mock_member = Mock()
        mock_member.roles = [Mock(id=111111111)]  # Member has tracked role
        
        is_tracked = await activity_tracker.is_member_tracked(mock_member)
        if is_tracked:
            print("   ✅ Member tracking detection works")
        else:
            print("   ❌ Member tracking detection failed")
            return False
        
        print("\n6. Testing Persistence Across Simulated Restart...")
        
        # Save current state
        current_tracked = activity_tracker.tracked_roles.copy()
        current_access = activity_tracker.authorized_roles.copy()
        
        # Simulate restart by creating new tracker instance
        from activity_tracker import ActivityTracker
        new_tracker = ActivityTracker()
        await new_tracker.initialize()
        
        # Check if configuration loaded correctly
        if new_tracker.tracked_roles == current_tracked:
            print("   ✅ Tracked roles persist across restart")
        else:
            print(f"   ❌ Tracked roles lost. Expected: {current_tracked}, Got: {new_tracker.tracked_roles}")
            return False
        
        if new_tracker.authorized_roles == current_access:
            print("   ✅ Access roles persist across restart")
        else:
            print(f"   ❌ Access roles lost. Expected: {current_access}, Got: {new_tracker.authorized_roles}")
            return False
        
        print("\n" + "=" * 65)
        print("🎉 ALL VERIFICATION TESTS PASSED!")
        print("\nConfiguration Persistence Fix Summary:")
        print("✅ Database collections initialize correctly")
        print("✅ Configuration saves to MongoDB properly")
        print("✅ Configuration loads from MongoDB correctly")
        print("✅ Modal workflow integration works")
        print("✅ Error handling is robust")
        print("✅ Dashboard integration functions properly")
        print("✅ Configuration persists across bot restarts")
        
        print("\nThe activity tracker configuration persistence issues have been resolved!")
        print("\nYou can now:")
        print("• Use /activity_setup to configure tracked and access roles")
        print("• Restart the bot and configuration will persist")
        print("• Use /activity_search to access the role-based dashboard")
        print("• All configured roles will be available in the dashboard")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Verification failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_specific_fixes():
    """Test the specific fixes that were implemented"""
    print("\n🔧 Testing Specific Fixes")
    print("-" * 30)
    
    try:
        from activity_tracker import activity_tracker
        
        print("1. Testing MongoDB collection boolean check fix...")
        
        # This should not raise an error anymore
        try:
            if activity_tracker.config_collection is None:
                print("   ✅ Collection None check works correctly")
            else:
                print("   ✅ Collection is properly initialized")
        except NotImplementedError:
            print("   ❌ Collection boolean check still broken")
            return False
        
        print("2. Testing enhanced error handling...")
        
        # Test save with proper error handling
        original_tracked = activity_tracker.tracked_roles.copy()
        activity_tracker.tracked_roles = {999999999}
        
        save_result = await activity_tracker._save_config()
        if save_result:
            print("   ✅ Enhanced save error handling works")
        else:
            print("   ❌ Enhanced save error handling failed")
            return False
        
        # Restore original state
        activity_tracker.tracked_roles = original_tracked
        await activity_tracker._save_config()
        
        print("3. Testing improved logging...")
        
        # Check if debug logging is working
        import logging
        logger = logging.getLogger('activity_tracker')
        original_level = logger.level
        logger.setLevel(logging.DEBUG)
        
        await activity_tracker._save_config()
        
        logger.setLevel(original_level)
        print("   ✅ Improved logging works correctly")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Specific fixes test failed: {e}")
        return False

async def main():
    """Main verification function"""
    print("🚀 Activity Tracker Configuration Persistence Fix Verification")
    print("=" * 70)
    
    # Run main verification
    main_result = await verify_configuration_persistence()
    
    if main_result:
        # Test specific fixes
        fixes_result = await test_specific_fixes()
        
        if fixes_result:
            print("\n" + "=" * 70)
            print("🎉 VERIFICATION COMPLETE - ALL ISSUES FIXED!")
            print("\nThe activity tracker configuration persistence issues have been resolved.")
            print("The system is now ready for production use.")
            return True
    
    print("\n" + "=" * 70)
    print("❌ VERIFICATION FAILED!")
    print("\nSome issues remain. Please check the error messages above.")
    return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Verification interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Verification failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
