#!/usr/bin/env python3
"""
Diagnostic script to identify why the application button registry is empty.
This will help troubleshoot the restoration process.
"""

import json
import os
from datetime import datetime

def load_applications_status():
    """Load the applications_status.json file"""
    try:
        if os.path.exists("applications_status.json"):
            with open("applications_status.json", "r") as f:
                return json.load(f)
        else:
            print("❌ applications_status.json file not found!")
            return {}
    except Exception as e:
        print(f"❌ Error loading applications_status.json: {e}")
        return {}

def analyze_applications_data():
    """Analyze the applications data to understand the registry issue"""
    print("🔍 Application Button Registry Diagnostic")
    print("=" * 60)
    
    # Load applications data
    applications_status = load_applications_status()
    
    if not applications_status:
        print("❌ No applications data found!")
        print("\n🔧 Possible causes:")
        print("   1. No applications have been submitted yet")
        print("   2. applications_status.json file is missing or corrupted")
        print("   3. File permissions issue")
        return
    
    print(f"📊 Total applications in file: {len(applications_status)}")
    
    # Analyze application statuses
    total_apps = len(applications_status)
    responded_apps = 0
    pending_apps = 0
    apps_with_message_id = 0
    apps_without_message_id = 0
    
    pending_details = []
    
    for user_id, status in applications_status.items():
        has_responded = status.get("responded", False)
        has_message_id = bool(status.get("message_id"))
        
        if has_responded:
            responded_apps += 1
        else:
            pending_apps += 1
            
        if has_message_id:
            apps_with_message_id += 1
            if not has_responded:
                pending_details.append({
                    "user_id": user_id,
                    "message_id": status.get("message_id"),
                    "app_name": status.get("application_name", "Unknown"),
                    "submission_time": status.get("submission_time", "Unknown")
                })
        else:
            apps_without_message_id += 1
    
    print(f"\n📈 Application Statistics:")
    print(f"   Total applications: {total_apps}")
    print(f"   Responded applications: {responded_apps}")
    print(f"   Pending applications: {pending_apps}")
    print(f"   Apps with message_id: {apps_with_message_id}")
    print(f"   Apps without message_id: {apps_without_message_id}")
    
    # Check for restoration candidates
    restoration_candidates = [app for app in pending_details]
    
    print(f"\n🔄 Restoration Analysis:")
    print(f"   Applications that should be restored: {len(restoration_candidates)}")
    
    if restoration_candidates:
        print(f"\n📋 Applications that should have views in registry:")
        for i, app in enumerate(restoration_candidates, 1):
            expected_key = f"{app['user_id']}_{app['message_id']}"
            print(f"   {i}. User: {app['user_id']}")
            print(f"      Message ID: {app['message_id']}")
            print(f"      Application: {app['app_name']}")
            print(f"      Expected registry key: '{expected_key}'")
            print(f"      Submission time: {app['submission_time']}")
            print()
        
        print("🚨 ISSUE IDENTIFIED:")
        print(f"   The registry should contain {len(restoration_candidates)} views but is empty!")
        print("\n🔧 Possible causes:")
        print("   1. restore_application_buttons() function failed during startup")
        print("   2. Application log channel is not configured or not found")
        print("   3. Messages were deleted from the application log channel")
        print("   4. Bot lacks permissions to fetch messages from the channel")
        print("   5. Error occurred during view creation or registration")
        print("   6. Registry was cleared after restoration completed")
        
        print("\n📝 Troubleshooting steps:")
        print("   1. Check bot startup logs for restoration errors")
        print("   2. Verify application_log_channel is set correctly")
        print("   3. Check if messages exist in the application log channel")
        print("   4. Verify bot has 'Read Message History' permission")
        print("   5. Look for any errors during view creation/registration")
        
    else:
        print("\n✅ No pending applications found that need restoration")
        print("   This explains why the registry is empty - it's expected!")
        
        if pending_apps > 0:
            print(f"\n⚠️ However, there are {pending_apps} pending applications without message_id:")
            for user_id, status in applications_status.items():
                if not status.get("responded", False) and not status.get("message_id"):
                    print(f"     - User {user_id}: {status.get('application_name', 'Unknown')}")
            print("   These applications may have been submitted but not logged properly")

def check_config_files():
    """Check if configuration files exist"""
    print("\n🔧 Configuration File Check:")
    
    config_files = [
        "applications_status.json",
        "data.json",
        "config.json"
    ]
    
    for file_name in config_files:
        if os.path.exists(file_name):
            try:
                size = os.path.getsize(file_name)
                modified = datetime.fromtimestamp(os.path.getmtime(file_name))
                print(f"   ✅ {file_name}: {size} bytes, modified {modified}")
            except Exception as e:
                print(f"   ⚠️ {file_name}: exists but error reading details - {e}")
        else:
            print(f"   ❌ {file_name}: not found")

def check_application_log_channel():
    """Check application log channel configuration"""
    print("\n📺 Application Log Channel Check:")
    
    try:
        # Try to load the channel ID from config
        config_files = ["data.json", "config.json"]
        channel_id = None
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, "r") as f:
                        data = json.load(f)
                        if "application_log_channel" in data:
                            channel_id = data["application_log_channel"]
                            print(f"   ✅ Found application_log_channel in {config_file}: {channel_id}")
                            break
                except Exception as e:
                    print(f"   ⚠️ Error reading {config_file}: {e}")
        
        if not channel_id:
            print("   ❌ application_log_channel not found in configuration!")
            print("   This would cause restoration to fail immediately")
        
    except Exception as e:
        print(f"   ❌ Error checking configuration: {e}")

def main():
    """Run all diagnostics"""
    analyze_applications_data()
    check_config_files()
    check_application_log_channel()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("If the registry is empty but there are pending applications with message_ids,")
    print("then the restoration process is failing. Check the bot startup logs for:")
    print("   • 'Starting application log button restoration process...'")
    print("   • Any error messages during restoration")
    print("   • 'Application button restoration complete'")
    print("   • The diagnostic output showing registry contents")

if __name__ == "__main__":
    main()
