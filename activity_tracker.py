"""
Advanced Discord Member Activity Tracking System

This module provides comprehensive activity tracking for Discord server members
with specific roles, including message counts, voice channel activity, and
presence tracking with high accuracy and performance optimization.

Features:
- Real-time message tracking per channel
- Voice channel join/leave tracking with precise durations
- Presence-based active hours calculation
- Role-based filtering and access control
- Professional embed-based data presentation
- Efficient database storage and querying
- Time period filtering (daily, weekly, monthly)
"""

import discord
from discord.ext import commands, tasks
from discord import app_commands
from discord.ui import View, Button, Select, Modal, TextInput
import asyncio
import logging
import traceback
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
import pymongo
from pymongo import MongoClient, ASCENDING, DESCENDING
from bson import ObjectId
import time
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

# Import bot instance and database
from bot_instance import bot
from database import db_manager

# Configure logging
logger = logging.getLogger('activity_tracker')

class ActivityPeriod(Enum):
    """Time period options for activity filtering"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"

@dataclass
class VoiceSession:
    """Represents a voice channel session"""
    channel_id: int
    channel_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: int = 0

@dataclass
class MessageActivity:
    """Represents message activity in a channel"""
    channel_id: int
    channel_name: str
    message_count: int
    last_message_time: datetime

@dataclass
class MemberActivityData:
    """Complete activity data for a member"""
    user_id: int
    username: str
    display_name: str
    tracked_roles: List[int]
    total_messages: int
    total_voice_time: int  # seconds
    total_active_hours: float
    voice_sessions: List[VoiceSession]
    message_activity: List[MessageActivity]
    last_seen: datetime
    tracking_start: datetime

class ActivityTracker:
    """Main activity tracking system"""
    
    def __init__(self):
        self.db = None
        self.activity_collection = None
        self.config_collection = None
        self.voice_sessions = {}  # user_id -> VoiceSession
        self.presence_cache = {}  # user_id -> last_presence_update
        self.message_cache = defaultdict(lambda: defaultdict(int))  # user_id -> channel_id -> count
        self.tracked_roles = set()
        self.authorized_roles = set()
        self.is_initialized = False
        
        # Performance optimization settings
        self.batch_size = 100
        self.cache_flush_interval = 300  # 5 minutes
        self.presence_update_threshold = 60  # 1 minute
        
        # Color scheme matching bot preferences
        self.colors = {
            'primary': 0x2b2d31,      # Modern dark theme
            'secondary': 0x5865f2,    # Discord blurple
            'success': 0x57f287,      # Green
            'warning': 0xfee75c,      # Yellow
            'error': 0xed4245,        # Red
            'info': 0x00d4ff,         # Cyan
            'neutral': 0x99aab5       # Light gray
        }
        
    async def initialize(self):
        """Initialize the activity tracker with database connections"""
        try:
            # Connect to MongoDB
            if not db_manager.is_connected:
                await db_manager.connect()
            
            self.db = db_manager.db
            self.activity_collection = self.db['member_activity']
            self.config_collection = self.db['activity_config']
            
            # Create indexes for optimal performance
            await self._create_indexes()
            
            # Load configuration
            await self._load_config()
            
            # Start background tasks
            self._start_background_tasks()
            
            self.is_initialized = True
            logger.info("Activity tracker initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize activity tracker: {e}")
            traceback.print_exc()
            raise
    
    async def _create_indexes(self):
        """Create database indexes for optimal query performance"""
        try:
            # Activity collection indexes
            await self.activity_collection.create_index([
                ("user_id", ASCENDING),
                ("guild_id", ASCENDING)
            ])
            
            await self.activity_collection.create_index([
                ("user_id", ASCENDING),
                ("date", DESCENDING)
            ])
            
            await self.activity_collection.create_index([
                ("guild_id", ASCENDING),
                ("date", DESCENDING)
            ])
            
            await self.activity_collection.create_index([
                ("tracked_roles", ASCENDING),
                ("date", DESCENDING)
            ])
            
            # Voice sessions indexes
            await self.activity_collection.create_index([
                ("voice_sessions.channel_id", ASCENDING),
                ("voice_sessions.start_time", DESCENDING)
            ])
            
            # Message activity indexes
            await self.activity_collection.create_index([
                ("message_activity.channel_id", ASCENDING),
                ("date", DESCENDING)
            ])
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating database indexes: {e}")
    
    async def _load_config(self):
        """Load tracker configuration from database"""
        try:
            config = await self.config_collection.find_one({"_id": "activity_config"})
            if config:
                self.tracked_roles = set(config.get("tracked_roles", []))
                self.authorized_roles = set(config.get("authorized_roles", []))
            else:
                # Initialize default config
                await self._save_config()
                
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
    
    async def _save_config(self):
        """Save tracker configuration to database"""
        try:
            config = {
                "_id": "activity_config",
                "tracked_roles": list(self.tracked_roles),
                "authorized_roles": list(self.authorized_roles),
                "last_updated": datetime.now(timezone.utc)
            }
            
            await self.config_collection.replace_one(
                {"_id": "activity_config"},
                config,
                upsert=True
            )
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")

    def _start_background_tasks(self):
        """Start background tasks for cache management"""
        if not hasattr(self, '_tasks_started'):
            self.flush_cache_task.start()
            self._tasks_started = True

    @tasks.loop(seconds=300)  # 5 minutes
    async def flush_cache_task(self):
        """Periodically flush cached data to database"""
        if not self.is_initialized:
            return

        try:
            await self._flush_message_cache()
            # Note: Presence data is updated in real-time, no batch processing needed
        except Exception as e:
            logger.error(f"Error in flush cache task: {e}")

    async def _flush_message_cache(self):
        """Flush message cache to database"""
        if not self.message_cache:
            return

        try:
            current_time = datetime.now(timezone.utc)
            current_date = current_time.date()

            for user_id, channel_data in self.message_cache.items():
                for channel_id, count in channel_data.items():
                    if count > 0:
                        await self._update_message_activity(
                            user_id, channel_id, count, current_time
                        )

            # Clear cache after flushing
            self.message_cache.clear()

        except Exception as e:
            logger.error(f"Error flushing message cache: {e}")

    async def _update_message_activity(self, user_id: int, channel_id: int,
                                     count: int, timestamp: datetime):
        """Update message activity in database"""
        try:
            date_key = timestamp.date().isoformat()

            # Get channel name
            channel = bot.get_channel(channel_id)
            channel_name = channel.name if channel else f"Unknown-{channel_id}"

            # Update or create activity record
            await self.activity_collection.update_one(
                {
                    "user_id": user_id,
                    "guild_id": channel.guild.id if channel else None,
                    "date": date_key
                },
                {
                    "$inc": {
                        "total_messages": count,
                        f"message_activity.{channel_id}.message_count": count
                    },
                    "$set": {
                        f"message_activity.{channel_id}.channel_name": channel_name,
                        f"message_activity.{channel_id}.last_message_time": timestamp,
                        "last_updated": timestamp
                    }
                },
                upsert=True
            )

        except Exception as e:
            logger.error(f"Error updating message activity: {e}")

    async def is_member_tracked(self, member: discord.Member) -> bool:
        """Check if a member should be tracked based on their roles"""
        if not self.tracked_roles:
            return False

        member_role_ids = {role.id for role in member.roles}
        return bool(self.tracked_roles.intersection(member_role_ids))

    async def has_access_permission(self, member: discord.Member) -> bool:
        """Check if a member has permission to access activity data"""
        if not self.authorized_roles:
            return False

        member_role_ids = {role.id for role in member.roles}
        return bool(self.authorized_roles.intersection(member_role_ids))

    async def track_message(self, message: discord.Message):
        """Track a message for activity monitoring"""
        if not self.is_initialized or not message.guild:
            return

        try:
            # Check if member should be tracked
            if not await self.is_member_tracked(message.author):
                return

            # Update message cache
            self.message_cache[message.author.id][message.channel.id] += 1

            # Update presence cache
            self.presence_cache[message.author.id] = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(f"Error tracking message: {e}")

    async def track_voice_state_update(self, member: discord.Member,
                                     before: discord.VoiceState,
                                     after: discord.VoiceState):
        """Track voice channel state changes"""
        if not self.is_initialized or not member.guild:
            return

        try:
            # Check if member should be tracked
            if not await self.is_member_tracked(member):
                return

            current_time = datetime.now(timezone.utc)
            user_id = member.id

            # Handle leaving voice channel
            if before.channel and user_id in self.voice_sessions:
                session = self.voice_sessions[user_id]
                session.end_time = current_time
                session.duration_seconds = int(
                    (current_time - session.start_time).total_seconds()
                )

                # Save completed session to database
                await self._save_voice_session(member, session)
                del self.voice_sessions[user_id]

            # Handle joining voice channel
            if after.channel:
                session = VoiceSession(
                    channel_id=after.channel.id,
                    channel_name=after.channel.name,
                    start_time=current_time
                )
                self.voice_sessions[user_id] = session

            # Update presence cache
            self.presence_cache[user_id] = current_time

        except Exception as e:
            logger.error(f"Error tracking voice state update: {e}")

    async def _save_voice_session(self, member: discord.Member, session: VoiceSession):
        """Save a completed voice session to database"""
        try:
            date_key = session.start_time.date().isoformat()

            session_data = {
                "channel_id": session.channel_id,
                "channel_name": session.channel_name,
                "start_time": session.start_time,
                "end_time": session.end_time,
                "duration_seconds": session.duration_seconds
            }

            await self.activity_collection.update_one(
                {
                    "user_id": member.id,
                    "guild_id": member.guild.id,
                    "date": date_key
                },
                {
                    "$push": {"voice_sessions": session_data},
                    "$inc": {"total_voice_time": session.duration_seconds},
                    "$set": {"last_updated": datetime.now(timezone.utc)}
                },
                upsert=True
            )

        except Exception as e:
            logger.error(f"Error saving voice session: {e}")

    async def track_presence_update(self, member: discord.Member,
                                   before: discord.Status, after: discord.Status):
        """Track presence updates for active hours calculation"""
        if not self.is_initialized or not member.guild:
            return

        try:
            # Check if member should be tracked
            if not await self.is_member_tracked(member):
                return

            current_time = datetime.now(timezone.utc)
            user_id = member.id

            # Update presence cache if status indicates activity
            if after in (discord.Status.online, discord.Status.idle, discord.Status.dnd):
                self.presence_cache[user_id] = current_time

        except Exception as e:
            logger.error(f"Error tracking presence update: {e}")

    async def get_member_activity(self, member: discord.Member,
                                period: ActivityPeriod = ActivityPeriod.WEEKLY) -> Optional[MemberActivityData]:
        """Get comprehensive activity data for a member"""
        try:
            # Calculate date range based on period
            end_date = datetime.now(timezone.utc).date()

            if period == ActivityPeriod.DAILY:
                start_date = end_date
            elif period == ActivityPeriod.WEEKLY:
                start_date = end_date - timedelta(days=7)
            else:  # MONTHLY
                start_date = end_date - timedelta(days=30)

            # Query database for activity data
            pipeline = [
                {
                    "$match": {
                        "user_id": member.id,
                        "guild_id": member.guild.id,
                        "date": {
                            "$gte": start_date.isoformat(),
                            "$lte": end_date.isoformat()
                        }
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id",
                        "total_messages": {"$sum": "$total_messages"},
                        "total_voice_time": {"$sum": "$total_voice_time"},
                        "voice_sessions": {"$push": "$voice_sessions"},
                        "message_activity": {"$push": "$message_activity"},
                        "last_seen": {"$max": "$last_updated"}
                    }
                }
            ]

            result = await self.activity_collection.aggregate(pipeline).to_list(1)

            if not result:
                return None

            data = result[0]

            # Process voice sessions
            voice_sessions = []
            for session_group in data.get("voice_sessions", []):
                if session_group:
                    for session in session_group:
                        voice_sessions.append(VoiceSession(
                            channel_id=session["channel_id"],
                            channel_name=session["channel_name"],
                            start_time=session["start_time"],
                            end_time=session.get("end_time"),
                            duration_seconds=session.get("duration_seconds", 0)
                        ))

            # Process message activity
            message_activity = []
            channel_totals = defaultdict(lambda: {"count": 0, "last_time": None, "name": ""})

            for activity_group in data.get("message_activity", []):
                if activity_group:
                    for channel_id_str, activity in activity_group.items():
                        if channel_id_str.isdigit():
                            channel_id = int(channel_id_str)
                            channel_totals[channel_id]["count"] += activity.get("message_count", 0)
                            channel_totals[channel_id]["name"] = activity.get("channel_name", "")

                            last_time = activity.get("last_message_time")
                            if last_time and (not channel_totals[channel_id]["last_time"] or
                                            last_time > channel_totals[channel_id]["last_time"]):
                                channel_totals[channel_id]["last_time"] = last_time

            for channel_id, totals in channel_totals.items():
                if totals["count"] > 0:
                    message_activity.append(MessageActivity(
                        channel_id=channel_id,
                        channel_name=totals["name"],
                        message_count=totals["count"],
                        last_message_time=totals["last_time"]
                    ))

            # Calculate active hours (simplified estimation)
            total_active_hours = data.get("total_voice_time", 0) / 3600.0

            # Get member's tracked roles
            tracked_roles = [role.id for role in member.roles if role.id in self.tracked_roles]

            return MemberActivityData(
                user_id=member.id,
                username=member.name,
                display_name=member.display_name,
                tracked_roles=tracked_roles,
                total_messages=data.get("total_messages", 0),
                total_voice_time=data.get("total_voice_time", 0),
                total_active_hours=total_active_hours,
                voice_sessions=voice_sessions,
                message_activity=message_activity,
                last_seen=data.get("last_seen", datetime.now(timezone.utc)),
                tracking_start=start_date
            )

        except Exception as e:
            logger.error(f"Error getting member activity: {e}")
            return None

    async def format_duration(self, seconds: int) -> str:
        """Format duration in seconds to human-readable string"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            return f"{minutes}m {remaining_seconds}s"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            return f"{hours}h {remaining_minutes}m"

class ActivitySetupView(View):
    """Interactive setup view for activity tracking configuration"""

    def __init__(self, tracker: ActivityTracker):
        super().__init__(timeout=300)
        self.tracker = tracker

    @discord.ui.button(label="Configure Tracked Roles", style=discord.ButtonStyle.primary, emoji="👥")
    async def configure_tracked_roles(self, interaction: discord.Interaction, button: Button):
        """Configure which roles should be tracked"""
        modal = TrackedRolesModal(self.tracker)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="Configure Access Roles", style=discord.ButtonStyle.secondary, emoji="🔐")
    async def configure_access_roles(self, interaction: discord.Interaction, button: Button):
        """Configure which roles can access activity data"""
        modal = AccessRolesModal(self.tracker)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="View Current Config", style=discord.ButtonStyle.success, emoji="📋")
    async def view_config(self, interaction: discord.Interaction, button: Button):
        """View current configuration"""
        embed = await self._create_config_embed(interaction.guild)
        await interaction.response.send_message(embed=embed, ephemeral=True)

    async def _create_config_embed(self, guild: discord.Guild) -> discord.Embed:
        """Create configuration display embed"""
        embed = discord.Embed(
            title="🔧 Activity Tracker Configuration",
            description="Current tracking and access settings",
            color=self.tracker.colors['primary']
        )

        # Tracked roles
        tracked_role_names = []
        for role_id in self.tracker.tracked_roles:
            role = guild.get_role(role_id)
            if role:
                tracked_role_names.append(role.name)

        embed.add_field(
            name="👥 Tracked Roles",
            value="\n".join([f"• {name}" for name in tracked_role_names]) or "None configured",
            inline=False
        )

        # Access roles
        access_role_names = []
        for role_id in self.tracker.authorized_roles:
            role = guild.get_role(role_id)
            if role:
                access_role_names.append(role.name)

        embed.add_field(
            name="🔐 Access Roles",
            value="\n".join([f"• {name}" for name in access_role_names]) or "None configured",
            inline=False
        )

        embed.set_footer(text="Use the buttons above to modify configuration")
        return embed

class TrackedRolesModal(Modal):
    """Modal for configuring tracked roles"""

    def __init__(self, tracker: ActivityTracker):
        super().__init__(title="Configure Tracked Roles")
        self.tracker = tracker

        self.role_input = TextInput(
            label="Role Names or IDs",
            placeholder="Enter role names or IDs separated by commas",
            style=discord.TextStyle.paragraph,
            max_length=1000,
            required=True
        )
        self.add_item(self.role_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Process tracked roles configuration"""
        try:
            role_inputs = [r.strip() for r in self.role_input.value.split(",")]
            new_tracked_roles = set()

            for role_input in role_inputs:
                if role_input.isdigit():
                    # Role ID
                    role = interaction.guild.get_role(int(role_input))
                else:
                    # Role name
                    role = discord.utils.get(interaction.guild.roles, name=role_input)

                if role:
                    new_tracked_roles.add(role.id)

            self.tracker.tracked_roles = new_tracked_roles
            await self.tracker._save_config()

            embed = discord.Embed(
                title="✅ Configuration Updated",
                description=f"Successfully configured {len(new_tracked_roles)} tracked roles",
                color=self.tracker.colors['success']
            )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error configuring tracked roles: {e}")
            embed = discord.Embed(
                title="❌ Configuration Error",
                description="Failed to update tracked roles configuration",
                color=self.tracker.colors['error']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class AccessRolesModal(Modal):
    """Modal for configuring access roles"""

    def __init__(self, tracker: ActivityTracker):
        super().__init__(title="Configure Access Roles")
        self.tracker = tracker

        self.role_input = TextInput(
            label="Role Names or IDs",
            placeholder="Enter role names or IDs separated by commas",
            style=discord.TextStyle.paragraph,
            max_length=1000,
            required=True
        )
        self.add_item(self.role_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Process access roles configuration"""
        try:
            role_inputs = [r.strip() for r in self.role_input.value.split(",")]
            new_access_roles = set()

            for role_input in role_inputs:
                if role_input.isdigit():
                    # Role ID
                    role = interaction.guild.get_role(int(role_input))
                else:
                    # Role name
                    role = discord.utils.get(interaction.guild.roles, name=role_input)

                if role:
                    new_access_roles.add(role.id)

            self.tracker.authorized_roles = new_access_roles
            await self.tracker._save_config()

            embed = discord.Embed(
                title="✅ Configuration Updated",
                description=f"Successfully configured {len(new_access_roles)} access roles",
                color=self.tracker.colors['success']
            )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error configuring access roles: {e}")
            embed = discord.Embed(
                title="❌ Configuration Error",
                description="Failed to update access roles configuration",
                color=self.tracker.colors['error']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class ActivityDisplayView(View):
    """Interactive view for displaying activity data with period filtering"""

    def __init__(self, tracker: ActivityTracker, member: discord.Member,
                 current_period: ActivityPeriod = ActivityPeriod.WEEKLY):
        super().__init__(timeout=300)
        self.tracker = tracker
        self.member = member
        self.current_period = current_period

    @discord.ui.button(label="Daily", style=discord.ButtonStyle.secondary, emoji="📅")
    async def daily_view(self, interaction: discord.Interaction, button: Button):
        """Switch to daily view"""
        await self._update_view(interaction, ActivityPeriod.DAILY)

    @discord.ui.button(label="Weekly", style=discord.ButtonStyle.primary, emoji="📊")
    async def weekly_view(self, interaction: discord.Interaction, button: Button):
        """Switch to weekly view"""
        await self._update_view(interaction, ActivityPeriod.WEEKLY)

    @discord.ui.button(label="Monthly", style=discord.ButtonStyle.success, emoji="📈")
    async def monthly_view(self, interaction: discord.Interaction, button: Button):
        """Switch to monthly view"""
        await self._update_view(interaction, ActivityPeriod.MONTHLY)

    async def _update_view(self, interaction: discord.Interaction, period: ActivityPeriod):
        """Update the view with new period data"""
        try:
            self.current_period = period

            # Update button styles
            for item in self.children:
                if isinstance(item, Button):
                    if (period == ActivityPeriod.DAILY and "Daily" in item.label) or \
                       (period == ActivityPeriod.WEEKLY and "Weekly" in item.label) or \
                       (period == ActivityPeriod.MONTHLY and "Monthly" in item.label):
                        item.style = discord.ButtonStyle.primary
                    else:
                        item.style = discord.ButtonStyle.secondary

            # Get updated activity data
            activity_data = await self.tracker.get_member_activity(self.member, period)

            if not activity_data:
                embed = discord.Embed(
                    title="📊 No Activity Data",
                    description=f"No activity data found for {self.member.display_name} in the {period.value} period",
                    color=self.tracker.colors['neutral']
                )
            else:
                embed = await self._create_activity_embed(activity_data)

            await interaction.response.edit_message(embed=embed, view=self)

        except Exception as e:
            logger.error(f"Error updating activity view: {e}")
            embed = discord.Embed(
                title="❌ Error",
                description="Failed to update activity data",
                color=self.tracker.colors['error']
            )
            await interaction.response.edit_message(embed=embed, view=self)

    async def _create_activity_embed(self, data: MemberActivityData) -> discord.Embed:
        """Create comprehensive activity embed"""
        embed = discord.Embed(
            title=f"📊 Activity Report - {data.display_name}",
            description=f"Comprehensive activity data for the {self.current_period.value} period",
            color=self.tracker.colors['primary']
        )

        # Set member avatar as thumbnail
        if self.member.avatar:
            embed.set_thumbnail(url=self.member.avatar.url)

        # Summary statistics
        embed.add_field(
            name="📈 Summary Statistics",
            value=f"```"
                  f"Total Messages: {data.total_messages:,}\n"
                  f"Voice Time: {await self.tracker.format_duration(data.total_voice_time)}\n"
                  f"Active Hours: {data.total_active_hours:.1f}h\n"
                  f"Last Seen: {data.last_seen.strftime('%Y-%m-%d %H:%M UTC')}"
                  f"```",
            inline=False
        )

        # Voice channel activity
        if data.voice_sessions:
            voice_summary = defaultdict(int)
            for session in data.voice_sessions:
                voice_summary[session.channel_name] += session.duration_seconds

            voice_text = []
            for channel_name, total_time in sorted(voice_summary.items(),
                                                 key=lambda x: x[1], reverse=True)[:5]:
                duration_str = await self.tracker.format_duration(total_time)
                voice_text.append(f"• **{channel_name}**: {duration_str}")

            embed.add_field(
                name="🎤 Voice Activity (Top 5)",
                value="\n".join(voice_text) or "No voice activity",
                inline=True
            )

        # Message activity
        if data.message_activity:
            sorted_messages = sorted(data.message_activity,
                                   key=lambda x: x.message_count, reverse=True)[:5]

            message_text = []
            for activity in sorted_messages:
                message_text.append(f"• **{activity.channel_name}**: {activity.message_count:,} messages")

            embed.add_field(
                name="💬 Message Activity (Top 5)",
                value="\n".join(message_text) or "No message activity",
                inline=True
            )

        # Tracked roles
        if data.tracked_roles:
            role_names = []
            for role_id in data.tracked_roles:
                role = self.member.guild.get_role(role_id)
                if role:
                    role_names.append(role.name)

            embed.add_field(
                name="🏷️ Tracked Roles",
                value="\n".join([f"• {name}" for name in role_names]),
                inline=False
            )

        embed.set_footer(text=f"Period: {self.current_period.value.title()} | Use buttons to switch periods")
        return embed

# Slash Commands
@bot.tree.command(name="activity_setup", description="Configure activity tracking system")
@app_commands.default_permissions(administrator=True)
async def activity_setup(interaction: discord.Interaction):
    """Setup command for activity tracking configuration"""
    try:
        if not activity_tracker.is_initialized:
            await activity_tracker.initialize()

        embed = discord.Embed(
            title="🔧 Activity Tracker Setup",
            description="Configure the member activity tracking system using the buttons below.\n\n"
                       "**Features:**\n"
                       "• Track message counts per channel\n"
                       "• Monitor voice channel activity\n"
                       "• Calculate active hours\n"
                       "• Role-based access control\n"
                       "• Professional data presentation",
            color=activity_tracker.colors['primary']
        )

        embed.add_field(
            name="📋 Setup Steps",
            value="1. **Configure Tracked Roles** - Select which roles to monitor\n"
                  "2. **Configure Access Roles** - Set who can view activity data\n"
                  "3. **View Configuration** - Review current settings",
            inline=False
        )

        embed.set_footer(text="Only users with configured access roles can view activity data")

        view = ActivitySetupView(activity_tracker)
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in activity setup command: {e}")
        embed = discord.Embed(
            title="❌ Setup Error",
            description="Failed to initialize activity tracking setup",
            color=activity_tracker.colors['error']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

async def period_autocomplete(interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
    """Autocomplete for period parameter"""
    periods = ["daily", "weekly", "monthly"]
    return [
        app_commands.Choice(name=period.title(), value=period)
        for period in periods if current.lower() in period.lower()
    ]

@bot.tree.command(name="activity_search", description="Search and view member activity data")
@app_commands.describe(
    member="The member to view activity data for",
    period="Time period for activity data (daily, weekly, monthly)"
)
@app_commands.autocomplete(period=period_autocomplete)
async def activity_search(interaction: discord.Interaction,
                         member: discord.Member,
                         period: str = "weekly"):
    """Search command for viewing member activity data with strict access control"""
    try:
        if not activity_tracker.is_initialized:
            await activity_tracker.initialize()

        # Strict permission check - only configured roles have access
        if not await activity_tracker.has_access_permission(interaction.user):
            embed = discord.Embed(
                title="🔐 Access Denied",
                description="You do not have permission to access activity data.\n"
                           "Contact an administrator to configure your access.",
                color=activity_tracker.colors['error']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Validate period parameter
        period_map = {
            "daily": ActivityPeriod.DAILY,
            "weekly": ActivityPeriod.WEEKLY,
            "monthly": ActivityPeriod.MONTHLY
        }

        activity_period = period_map.get(period.lower(), ActivityPeriod.WEEKLY)

        # Check if member is tracked
        if not await activity_tracker.is_member_tracked(member):
            embed = discord.Embed(
                title="📊 Member Not Tracked",
                description=f"{member.display_name} does not have any tracked roles.\n"
                           "Only members with configured tracked roles are monitored.",
                color=activity_tracker.colors['warning']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Defer response for data processing
        await interaction.response.defer(ephemeral=True)

        # Get activity data
        activity_data = await activity_tracker.get_member_activity(member, activity_period)

        if not activity_data:
            embed = discord.Embed(
                title="📊 No Activity Data",
                description=f"No activity data found for {member.display_name} in the {period} period",
                color=activity_tracker.colors['neutral']
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Create interactive view with activity data
        view = ActivityDisplayView(activity_tracker, member, activity_period)
        embed = await view._create_activity_embed(activity_data)

        await interaction.followup.send(embed=embed, view=view, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in activity search command: {e}")
        embed = discord.Embed(
            title="❌ Search Error",
            description="Failed to retrieve activity data",
            color=activity_tracker.colors['error']
        )

        if interaction.response.is_done():
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            await interaction.response.send_message(embed=embed, ephemeral=True)

# Event handlers are integrated into the main bot.py file
# to avoid conflicts with existing handlers

# Global activity tracker instance
activity_tracker = ActivityTracker()
