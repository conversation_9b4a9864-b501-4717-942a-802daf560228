"""
Advanced Discord Member Activity Tracking System

This module provides comprehensive activity tracking for Discord server members
with specific roles, including message counts, voice channel activity, and
presence tracking with high accuracy and performance optimization.

Features:
- Real-time message tracking per channel
- Voice channel join/leave tracking with precise durations
- Presence-based active hours calculation
- Role-based filtering and access control
- Professional embed-based data presentation
- Efficient database storage and querying
- Time period filtering (daily, weekly, monthly)
"""

import discord
from discord.ext import commands, tasks
from discord import app_commands
from discord.ui import View, Button, Select, Modal, TextInput
import asyncio
import logging
import traceback
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
import pymongo
from pymongo import MongoClient, ASCENDING, DESCENDING
from bson import ObjectId
import time
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import socket
import webbrowser
from urllib.parse import quote
import uuid

# Import bot instance and database
from bot_instance import bot
from database import db_manager

# Web framework imports
try:
    from flask import Flask, render_template, jsonify, request, session, redirect, url_for
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    logger.warning("Flask not available. Web interface will be disabled.")

# Configure logging
logger = logging.getLogger('activity_tracker')

class ActivityPeriod(Enum):
    """Time period options for activity filtering"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"

@dataclass
class VoiceSession:
    """Represents a voice channel session"""
    channel_id: int
    channel_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: int = 0

@dataclass
class MessageActivity:
    """Represents message activity in a channel"""
    channel_id: int
    channel_name: str
    message_count: int
    last_message_time: datetime

@dataclass
class MemberActivityData:
    """Complete activity data for a member"""
    user_id: int
    username: str
    display_name: str
    tracked_roles: List[int]
    total_messages: int
    total_voice_time: int  # seconds
    total_active_hours: float
    voice_sessions: List[VoiceSession]
    message_activity: List[MessageActivity]
    last_seen: datetime
    tracking_start: datetime

class ActivityTracker:
    """Main activity tracking system"""
    
    def __init__(self):
        self.db = None
        self.activity_collection = None
        self.config_collection = None
        self.voice_sessions = {}  # user_id -> VoiceSession
        self.presence_cache = {}  # user_id -> last_presence_update
        self.message_cache = defaultdict(lambda: defaultdict(int))  # user_id -> channel_id -> count
        self.tracked_roles = set()
        self.authorized_roles = set()
        self.is_initialized = False
        
        # Performance optimization settings
        self.batch_size = 100
        self.cache_flush_interval = 300  # 5 minutes
        self.presence_update_threshold = 60  # 1 minute
        
        # Color scheme matching bot preferences
        self.colors = {
            'primary': 0x2b2d31,      # Modern dark theme
            'secondary': 0x5865f2,    # Discord blurple
            'success': 0x57f287,      # Green
            'warning': 0xfee75c,      # Yellow
            'error': 0xed4245,        # Red
            'info': 0x00d4ff,         # Cyan
            'neutral': 0x99aab5       # Light gray
        }
        
    async def initialize(self):
        """Initialize the activity tracker with database connections"""
        try:
            # Connect to MongoDB
            if not db_manager.is_connected:
                await db_manager.connect()
            
            self.db = db_manager.db
            self.activity_collection = self.db['member_activity']
            self.config_collection = self.db['activity_config']
            
            # Create indexes for optimal performance
            await self._create_indexes()
            
            # Load configuration
            await self._load_config()
            
            # Start background tasks
            self._start_background_tasks()

            # Restore persistent dashboards
            await self.restore_persistent_dashboards()

            self.is_initialized = True
            logger.info("Activity tracker initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize activity tracker: {e}")
            traceback.print_exc()
            raise
    
    async def _create_indexes(self):
        """Create database indexes for optimal query performance"""
        try:
            # Activity collection indexes
            await self.activity_collection.create_index([
                ("user_id", ASCENDING),
                ("guild_id", ASCENDING)
            ])
            
            await self.activity_collection.create_index([
                ("user_id", ASCENDING),
                ("date", DESCENDING)
            ])
            
            await self.activity_collection.create_index([
                ("guild_id", ASCENDING),
                ("date", DESCENDING)
            ])
            
            await self.activity_collection.create_index([
                ("tracked_roles", ASCENDING),
                ("date", DESCENDING)
            ])
            
            # Voice sessions indexes
            await self.activity_collection.create_index([
                ("voice_sessions.channel_id", ASCENDING),
                ("voice_sessions.start_time", DESCENDING)
            ])
            
            # Message activity indexes
            await self.activity_collection.create_index([
                ("message_activity.channel_id", ASCENDING),
                ("date", DESCENDING)
            ])
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating database indexes: {e}")
    
    async def _load_config(self):
        """Load tracker configuration from database"""
        try:
            logger.info("Loading activity tracker configuration from database...")

            # Ensure we have a valid config collection
            if self.config_collection is None:
                logger.error("Config collection not initialized")
                return

            # Try to load existing configuration
            config = await self.config_collection.find_one({"_id": "activity_config"})

            if config:
                self.tracked_roles = set(config.get("tracked_roles", []))
                self.authorized_roles = set(config.get("authorized_roles", []))
                logger.info(f"Loaded configuration: {len(self.tracked_roles)} tracked roles, {len(self.authorized_roles)} access roles")

                # Log the actual role IDs for debugging
                logger.debug(f"Tracked roles: {list(self.tracked_roles)}")
                logger.debug(f"Authorized roles: {list(self.authorized_roles)}")
            else:
                logger.info("No existing configuration found, initializing with empty sets")
                self.tracked_roles = set()
                self.authorized_roles = set()
                # Save initial empty config
                await self._save_config()

        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            traceback.print_exc()
            # Initialize with empty sets on error
            self.tracked_roles = set()
            self.authorized_roles = set()

    async def _save_config(self):
        """Save tracker configuration to database"""
        try:
            logger.info("Saving activity tracker configuration to database...")

            # Ensure we have a valid config collection
            if self.config_collection is None:
                logger.error("Config collection not initialized, cannot save configuration")
                return False

            # Ensure we have valid sets (not None)
            tracked_roles = self.tracked_roles if self.tracked_roles is not None else set()
            authorized_roles = self.authorized_roles if self.authorized_roles is not None else set()

            config = {
                "_id": "activity_config",
                "tracked_roles": list(tracked_roles),
                "authorized_roles": list(authorized_roles),
                "last_updated": datetime.now(timezone.utc)
            }

            logger.debug(f"Saving config: {config}")

            # Use replace_one with upsert to ensure the document is saved
            result = await self.config_collection.replace_one(
                {"_id": "activity_config"},
                config,
                upsert=True
            )

            if result.upserted_id or result.modified_count > 0:
                logger.info(f"Configuration saved successfully: {len(tracked_roles)} tracked roles, {len(authorized_roles)} access roles")
                return True
            else:
                logger.warning("Configuration save operation completed but no changes detected")
                return True

        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            traceback.print_exc()
            return False

    async def test_configuration_persistence(self):
        """Test method to verify configuration persistence is working"""
        try:
            logger.info("Testing configuration persistence...")

            # Test saving configuration
            test_tracked_roles = {111, 222, 333}
            test_access_roles = {444, 555}

            self.tracked_roles = test_tracked_roles
            self.authorized_roles = test_access_roles

            save_result = await self._save_config()
            if not save_result:
                logger.error("Failed to save test configuration")
                return False

            # Test loading configuration
            self.tracked_roles = set()
            self.authorized_roles = set()

            await self._load_config()

            # Verify loaded configuration matches saved configuration
            if self.tracked_roles == test_tracked_roles and self.authorized_roles == test_access_roles:
                logger.info("✅ Configuration persistence test passed")
                return True
            else:
                logger.error(f"❌ Configuration persistence test failed")
                logger.error(f"Expected tracked roles: {test_tracked_roles}, got: {self.tracked_roles}")
                logger.error(f"Expected access roles: {test_access_roles}, got: {self.authorized_roles}")
                return False

        except Exception as e:
            logger.error(f"Error testing configuration persistence: {e}")
            traceback.print_exc()
            return False

    async def save_persistent_dashboard(self, view_id: str, guild_id: int,
                                      channel_id: int, message_id: int):
        """Save persistent dashboard information"""
        try:
            dashboard_data = {
                "_id": view_id,
                "guild_id": guild_id,
                "channel_id": channel_id,
                "message_id": message_id,
                "created_at": datetime.now(timezone.utc),
                "type": "activity_dashboard"
            }

            # Use the database manager's persistent views collection
            persistent_views_collection = self.db['persistent_views']
            await persistent_views_collection.replace_one(
                {"_id": view_id},
                dashboard_data,
                upsert=True
            )

        except Exception as e:
            logger.error(f"Error saving persistent dashboard: {e}")

    async def restore_persistent_dashboards(self):
        """Restore persistent dashboards after bot restart"""
        try:
            if not self.is_initialized:
                return

            persistent_views_collection = self.db['persistent_views']
            dashboards = await persistent_views_collection.find({
                "type": "activity_dashboard"
            }).to_list(None)

            restored_count = 0
            for dashboard_data in dashboards:
                try:
                    guild_id = dashboard_data["guild_id"]
                    channel_id = dashboard_data["channel_id"]
                    message_id = dashboard_data["message_id"]
                    view_id = dashboard_data["_id"]

                    guild = bot.get_guild(guild_id)
                    if not guild:
                        continue

                    channel = guild.get_channel(channel_id)
                    if not channel:
                        continue

                    try:
                        message = await channel.fetch_message(message_id)

                        # Create new persistent view
                        dashboard = PersistentRoleDashboardView(
                            self, guild_id, ActivityPeriod.WEEKLY, view_id
                        )

                        # Update role dropdown
                        await dashboard._update_role_dropdown()

                        # Register the view with the bot
                        bot.add_view(dashboard, message_id=message_id)

                        restored_count += 1
                        logger.info(f"Restored activity dashboard {view_id} in guild {guild.name}")

                    except discord.NotFound:
                        # Message was deleted, clean up the database entry
                        await persistent_views_collection.delete_one({"_id": view_id})
                        logger.info(f"Cleaned up deleted dashboard {view_id}")

                except Exception as e:
                    logger.error(f"Error restoring dashboard {dashboard_data.get('_id', 'unknown')}: {e}")

            if restored_count > 0:
                logger.info(f"Successfully restored {restored_count} activity dashboards")

        except Exception as e:
            logger.error(f"Error restoring persistent dashboards: {e}")

    def _start_background_tasks(self):
        """Start background tasks for cache management"""
        if not hasattr(self, '_tasks_started'):
            self.flush_cache_task.start()
            self._tasks_started = True

    @tasks.loop(seconds=300)  # 5 minutes
    async def flush_cache_task(self):
        """Periodically flush cached data to database"""
        if not self.is_initialized:
            return

        try:
            await self._flush_message_cache()
            # Note: Presence data is updated in real-time, no batch processing needed
        except Exception as e:
            logger.error(f"Error in flush cache task: {e}")

    async def _flush_message_cache(self):
        """Flush message cache to database"""
        if not self.message_cache:
            return

        try:
            current_time = datetime.now(timezone.utc)
            current_date = current_time.date()

            for user_id, channel_data in self.message_cache.items():
                for channel_id, count in channel_data.items():
                    if count > 0:
                        await self._update_message_activity(
                            user_id, channel_id, count, current_time
                        )

            # Clear cache after flushing
            self.message_cache.clear()

        except Exception as e:
            logger.error(f"Error flushing message cache: {e}")

    async def _update_message_activity(self, user_id: int, channel_id: int,
                                     count: int, timestamp: datetime):
        """Update message activity in database"""
        try:
            date_key = timestamp.date().isoformat()

            # Get channel name
            channel = bot.get_channel(channel_id)
            channel_name = channel.name if channel else f"Unknown-{channel_id}"

            # Update or create activity record
            await self.activity_collection.update_one(
                {
                    "user_id": user_id,
                    "guild_id": channel.guild.id if channel else None,
                    "date": date_key
                },
                {
                    "$inc": {
                        "total_messages": count,
                        f"message_activity.{channel_id}.message_count": count
                    },
                    "$set": {
                        f"message_activity.{channel_id}.channel_name": channel_name,
                        f"message_activity.{channel_id}.last_message_time": timestamp,
                        "last_updated": timestamp
                    }
                },
                upsert=True
            )

        except Exception as e:
            logger.error(f"Error updating message activity: {e}")

    async def is_member_tracked(self, member: discord.Member) -> bool:
        """Check if a member should be tracked based on their roles"""
        if not self.tracked_roles:
            return False

        member_role_ids = {role.id for role in member.roles}
        return bool(self.tracked_roles.intersection(member_role_ids))

    async def has_access_permission(self, member: discord.Member) -> bool:
        """Check if a member has permission to access activity data"""
        if not self.authorized_roles:
            return False

        member_role_ids = {role.id for role in member.roles}
        return bool(self.authorized_roles.intersection(member_role_ids))

    async def track_message(self, message: discord.Message):
        """Track a message for activity monitoring"""
        if not self.is_initialized or not message.guild:
            return

        try:
            # Check if member should be tracked
            if not await self.is_member_tracked(message.author):
                return

            # Update message cache
            self.message_cache[message.author.id][message.channel.id] += 1

            # Update presence cache
            self.presence_cache[message.author.id] = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(f"Error tracking message: {e}")

    async def track_voice_state_update(self, member: discord.Member,
                                     before: discord.VoiceState,
                                     after: discord.VoiceState):
        """Track voice channel state changes"""
        if not self.is_initialized or not member.guild:
            return

        try:
            # Check if member should be tracked
            if not await self.is_member_tracked(member):
                return

            current_time = datetime.now(timezone.utc)
            user_id = member.id

            # Handle leaving voice channel
            if before.channel and user_id in self.voice_sessions:
                session = self.voice_sessions[user_id]
                session.end_time = current_time
                session.duration_seconds = int(
                    (current_time - session.start_time).total_seconds()
                )

                # Save completed session to database
                await self._save_voice_session(member, session)
                del self.voice_sessions[user_id]

            # Handle joining voice channel
            if after.channel:
                session = VoiceSession(
                    channel_id=after.channel.id,
                    channel_name=after.channel.name,
                    start_time=current_time
                )
                self.voice_sessions[user_id] = session

            # Update presence cache
            self.presence_cache[user_id] = current_time

        except Exception as e:
            logger.error(f"Error tracking voice state update: {e}")

    async def _save_voice_session(self, member: discord.Member, session: VoiceSession):
        """Save a completed voice session to database"""
        try:
            date_key = session.start_time.date().isoformat()

            session_data = {
                "channel_id": session.channel_id,
                "channel_name": session.channel_name,
                "start_time": session.start_time,
                "end_time": session.end_time,
                "duration_seconds": session.duration_seconds
            }

            await self.activity_collection.update_one(
                {
                    "user_id": member.id,
                    "guild_id": member.guild.id,
                    "date": date_key
                },
                {
                    "$push": {"voice_sessions": session_data},
                    "$inc": {"total_voice_time": session.duration_seconds},
                    "$set": {"last_updated": datetime.now(timezone.utc)}
                },
                upsert=True
            )

        except Exception as e:
            logger.error(f"Error saving voice session: {e}")

    async def track_presence_update(self, member: discord.Member,
                                   before: discord.Status, after: discord.Status):
        """Track presence updates for active hours calculation"""
        if not self.is_initialized or not member.guild:
            return

        try:
            # Check if member should be tracked
            if not await self.is_member_tracked(member):
                return

            current_time = datetime.now(timezone.utc)
            user_id = member.id

            # Update presence cache if status indicates activity
            if after in (discord.Status.online, discord.Status.idle, discord.Status.dnd):
                self.presence_cache[user_id] = current_time

        except Exception as e:
            logger.error(f"Error tracking presence update: {e}")

    async def get_member_activity(self, member: discord.Member,
                                period: ActivityPeriod = ActivityPeriod.WEEKLY) -> Optional[MemberActivityData]:
        """Get comprehensive activity data for a member"""
        try:
            # Calculate date range based on period
            end_date = datetime.now(timezone.utc).date()

            if period == ActivityPeriod.DAILY:
                start_date = end_date
            elif period == ActivityPeriod.WEEKLY:
                start_date = end_date - timedelta(days=7)
            else:  # MONTHLY
                start_date = end_date - timedelta(days=30)

            # Query database for activity data
            pipeline = [
                {
                    "$match": {
                        "user_id": member.id,
                        "guild_id": member.guild.id,
                        "date": {
                            "$gte": start_date.isoformat(),
                            "$lte": end_date.isoformat()
                        }
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id",
                        "total_messages": {"$sum": "$total_messages"},
                        "total_voice_time": {"$sum": "$total_voice_time"},
                        "voice_sessions": {"$push": "$voice_sessions"},
                        "message_activity": {"$push": "$message_activity"},
                        "last_seen": {"$max": "$last_updated"}
                    }
                }
            ]

            result = await self.activity_collection.aggregate(pipeline).to_list(1)

            if not result:
                return None

            data = result[0]

            # Process voice sessions
            voice_sessions = []
            for session_group in data.get("voice_sessions", []):
                if session_group:
                    for session in session_group:
                        voice_sessions.append(VoiceSession(
                            channel_id=session["channel_id"],
                            channel_name=session["channel_name"],
                            start_time=session["start_time"],
                            end_time=session.get("end_time"),
                            duration_seconds=session.get("duration_seconds", 0)
                        ))

            # Process message activity
            message_activity = []
            channel_totals = defaultdict(lambda: {"count": 0, "last_time": None, "name": ""})

            for activity_group in data.get("message_activity", []):
                if activity_group:
                    for channel_id_str, activity in activity_group.items():
                        if channel_id_str.isdigit():
                            channel_id = int(channel_id_str)
                            channel_totals[channel_id]["count"] += activity.get("message_count", 0)
                            channel_totals[channel_id]["name"] = activity.get("channel_name", "")

                            last_time = activity.get("last_message_time")
                            if last_time and (not channel_totals[channel_id]["last_time"] or
                                            last_time > channel_totals[channel_id]["last_time"]):
                                channel_totals[channel_id]["last_time"] = last_time

            for channel_id, totals in channel_totals.items():
                if totals["count"] > 0:
                    message_activity.append(MessageActivity(
                        channel_id=channel_id,
                        channel_name=totals["name"],
                        message_count=totals["count"],
                        last_message_time=totals["last_time"]
                    ))

            # Calculate active hours (simplified estimation)
            total_active_hours = data.get("total_voice_time", 0) / 3600.0

            # Get member's tracked roles
            tracked_roles = [role.id for role in member.roles if role.id in self.tracked_roles]

            return MemberActivityData(
                user_id=member.id,
                username=member.name,
                display_name=member.display_name,
                tracked_roles=tracked_roles,
                total_messages=data.get("total_messages", 0),
                total_voice_time=data.get("total_voice_time", 0),
                total_active_hours=total_active_hours,
                voice_sessions=voice_sessions,
                message_activity=message_activity,
                last_seen=data.get("last_seen", datetime.now(timezone.utc)),
                tracking_start=start_date
            )

        except Exception as e:
            logger.error(f"Error getting member activity: {e}")
            return None

    async def format_duration(self, seconds: int) -> str:
        """Format duration in seconds to human-readable string"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            return f"{minutes}m {remaining_seconds}s"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            return f"{hours}h {remaining_minutes}m"

class ActivitySetupView(View):
    """Interactive setup view for activity tracking configuration"""

    def __init__(self, tracker: ActivityTracker):
        super().__init__(timeout=300)
        self.tracker = tracker

    @discord.ui.button(label="Configure Tracked Roles", style=discord.ButtonStyle.primary, emoji="👥")
    async def configure_tracked_roles(self, interaction: discord.Interaction, button: Button):
        """Configure which roles should be tracked"""
        modal = TrackedRolesModal(self.tracker)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="Configure Access Roles", style=discord.ButtonStyle.secondary, emoji="🔐")
    async def configure_access_roles(self, interaction: discord.Interaction, button: Button):
        """Configure which roles can access activity data"""
        modal = AccessRolesModal(self.tracker)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="View Current Config", style=discord.ButtonStyle.success, emoji="📋")
    async def view_config(self, interaction: discord.Interaction, button: Button):
        """View current configuration"""
        embed = await self._create_config_embed(interaction.guild)
        await interaction.response.send_message(embed=embed, ephemeral=True)

    async def _create_config_embed(self, guild: discord.Guild) -> discord.Embed:
        """Create configuration display embed"""
        embed = discord.Embed(
            title="🔧 Activity Tracker Configuration",
            description="Current tracking and access settings",
            color=self.tracker.colors['primary']
        )

        # Tracked roles
        tracked_role_names = []
        tracked_member_count = 0
        for role_id in self.tracker.tracked_roles:
            role = guild.get_role(role_id)
            if role:
                member_count = len([m for m in role.members if not m.bot])
                tracked_role_names.append(f"{role.name} ({member_count} members)")
                tracked_member_count += member_count
            else:
                tracked_role_names.append(f"Unknown Role (ID: {role_id})")

        tracked_value = "\n".join([f"• {name}" for name in tracked_role_names]) or "None configured"
        if tracked_role_names:
            tracked_value += f"\n\n**Total tracked members: {tracked_member_count}**"

        embed.add_field(
            name="👥 Tracked Roles",
            value=tracked_value,
            inline=False
        )

        # Access roles
        access_role_names = []
        access_member_count = 0
        for role_id in self.tracker.authorized_roles:
            role = guild.get_role(role_id)
            if role:
                member_count = len([m for m in role.members if not m.bot])
                access_role_names.append(f"{role.name} ({member_count} members)")
                access_member_count += member_count
            else:
                access_role_names.append(f"Unknown Role (ID: {role_id})")

        access_value = "\n".join([f"• {name}" for name in access_role_names]) or "None configured"
        if access_role_names:
            access_value += f"\n\n**Total authorized members: {access_member_count}**"

        embed.add_field(
            name="🔐 Access Roles",
            value=access_value,
            inline=False
        )

        # Configuration status
        status_text = []
        if self.tracker.tracked_roles:
            status_text.append("✅ Tracking enabled")
        else:
            status_text.append("⚠️ No roles being tracked")

        if self.tracker.authorized_roles:
            status_text.append("✅ Access control configured")
        else:
            status_text.append("⚠️ No access roles configured")

        embed.add_field(
            name="📊 Status",
            value=" | ".join(status_text),
            inline=False
        )

        embed.set_footer(text="Use the buttons above to modify configuration")
        return embed

class TrackedRolesModal(Modal):
    """Modal for configuring tracked roles"""

    def __init__(self, tracker: ActivityTracker):
        super().__init__(title="Configure Tracked Roles")
        self.tracker = tracker

        self.role_input = TextInput(
            label="Role Names or IDs",
            placeholder="Enter role names or IDs separated by commas",
            style=discord.TextStyle.paragraph,
            max_length=1000,
            required=True
        )
        self.add_item(self.role_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Process tracked roles configuration"""
        try:
            logger.info(f"Processing tracked roles configuration from user {interaction.user.id}")

            # Parse role inputs
            role_inputs = [r.strip() for r in self.role_input.value.split(",") if r.strip()]
            new_tracked_roles = set()
            invalid_roles = []

            logger.debug(f"Processing {len(role_inputs)} role inputs: {role_inputs}")

            for role_input in role_inputs:
                role = None
                if role_input.isdigit():
                    # Role ID
                    try:
                        role_id = int(role_input)
                        role = interaction.guild.get_role(role_id)
                        if not role:
                            invalid_roles.append(f"ID {role_input} (not found)")
                    except ValueError:
                        invalid_roles.append(f"Invalid ID: {role_input}")
                else:
                    # Role name
                    role = discord.utils.get(interaction.guild.roles, name=role_input)
                    if not role:
                        invalid_roles.append(f"Name '{role_input}' (not found)")

                if role:
                    new_tracked_roles.add(role.id)
                    logger.debug(f"Added role {role.name} (ID: {role.id}) to tracked roles")

            # Update tracker configuration
            old_count = len(self.tracker.tracked_roles)
            self.tracker.tracked_roles = new_tracked_roles

            # Save configuration
            save_success = await self.tracker._save_config()

            if save_success:
                logger.info(f"Successfully updated tracked roles: {old_count} -> {len(new_tracked_roles)}")

                # Create success embed
                embed = discord.Embed(
                    title="✅ Configuration Updated",
                    description=f"Successfully configured {len(new_tracked_roles)} tracked roles",
                    color=self.tracker.colors['success']
                )

                if new_tracked_roles:
                    role_names = []
                    for role_id in new_tracked_roles:
                        role = interaction.guild.get_role(role_id)
                        if role:
                            role_names.append(role.name)

                    embed.add_field(
                        name="Configured Roles",
                        value="\n".join([f"• {name}" for name in role_names]),
                        inline=False
                    )

                if invalid_roles:
                    embed.add_field(
                        name="⚠️ Invalid Roles (Skipped)",
                        value="\n".join([f"• {invalid}" for invalid in invalid_roles]),
                        inline=False
                    )
            else:
                embed = discord.Embed(
                    title="❌ Save Error",
                    description="Configuration updated but failed to save to database",
                    color=self.tracker.colors['error']
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error configuring tracked roles: {e}")
            traceback.print_exc()
            embed = discord.Embed(
                title="❌ Configuration Error",
                description=f"Failed to update tracked roles configuration: {str(e)}",
                color=self.tracker.colors['error']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class AccessRolesModal(Modal):
    """Modal for configuring access roles"""

    def __init__(self, tracker: ActivityTracker):
        super().__init__(title="Configure Access Roles")
        self.tracker = tracker

        self.role_input = TextInput(
            label="Role Names or IDs",
            placeholder="Enter role names or IDs separated by commas",
            style=discord.TextStyle.paragraph,
            max_length=1000,
            required=True
        )
        self.add_item(self.role_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Process access roles configuration"""
        try:
            logger.info(f"Processing access roles configuration from user {interaction.user.id}")

            # Parse role inputs
            role_inputs = [r.strip() for r in self.role_input.value.split(",") if r.strip()]
            new_access_roles = set()
            invalid_roles = []

            logger.debug(f"Processing {len(role_inputs)} role inputs: {role_inputs}")

            for role_input in role_inputs:
                role = None
                if role_input.isdigit():
                    # Role ID
                    try:
                        role_id = int(role_input)
                        role = interaction.guild.get_role(role_id)
                        if not role:
                            invalid_roles.append(f"ID {role_input} (not found)")
                    except ValueError:
                        invalid_roles.append(f"Invalid ID: {role_input}")
                else:
                    # Role name
                    role = discord.utils.get(interaction.guild.roles, name=role_input)
                    if not role:
                        invalid_roles.append(f"Name '{role_input}' (not found)")

                if role:
                    new_access_roles.add(role.id)
                    logger.debug(f"Added role {role.name} (ID: {role.id}) to access roles")

            # Update tracker configuration
            old_count = len(self.tracker.authorized_roles)
            self.tracker.authorized_roles = new_access_roles

            # Save configuration
            save_success = await self.tracker._save_config()

            if save_success:
                logger.info(f"Successfully updated access roles: {old_count} -> {len(new_access_roles)}")

                # Create success embed
                embed = discord.Embed(
                    title="✅ Configuration Updated",
                    description=f"Successfully configured {len(new_access_roles)} access roles",
                    color=self.tracker.colors['success']
                )

                if new_access_roles:
                    role_names = []
                    for role_id in new_access_roles:
                        role = interaction.guild.get_role(role_id)
                        if role:
                            role_names.append(role.name)

                    embed.add_field(
                        name="Configured Roles",
                        value="\n".join([f"• {name}" for name in role_names]),
                        inline=False
                    )

                if invalid_roles:
                    embed.add_field(
                        name="⚠️ Invalid Roles (Skipped)",
                        value="\n".join([f"• {invalid}" for invalid in invalid_roles]),
                        inline=False
                    )
            else:
                embed = discord.Embed(
                    title="❌ Save Error",
                    description="Configuration updated but failed to save to database",
                    color=self.tracker.colors['error']
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error configuring access roles: {e}")
            traceback.print_exc()
            embed = discord.Embed(
                title="❌ Configuration Error",
                description=f"Failed to update access roles configuration: {str(e)}",
                color=self.tracker.colors['error']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class ActivityDisplayView(View):
    """Interactive view for displaying activity data with period filtering"""

    def __init__(self, tracker: ActivityTracker, member: discord.Member,
                 current_period: ActivityPeriod = ActivityPeriod.WEEKLY):
        super().__init__(timeout=300)
        self.tracker = tracker
        self.member = member
        self.current_period = current_period

    @discord.ui.button(label="Daily", style=discord.ButtonStyle.secondary, emoji="📅")
    async def daily_view(self, interaction: discord.Interaction, button: Button):
        """Switch to daily view"""
        await self._update_view(interaction, ActivityPeriod.DAILY)

    @discord.ui.button(label="Weekly", style=discord.ButtonStyle.primary, emoji="📊")
    async def weekly_view(self, interaction: discord.Interaction, button: Button):
        """Switch to weekly view"""
        await self._update_view(interaction, ActivityPeriod.WEEKLY)

    @discord.ui.button(label="Monthly", style=discord.ButtonStyle.success, emoji="📈")
    async def monthly_view(self, interaction: discord.Interaction, button: Button):
        """Switch to monthly view"""
        await self._update_view(interaction, ActivityPeriod.MONTHLY)

    async def _update_view(self, interaction: discord.Interaction, period: ActivityPeriod):
        """Update the view with new period data"""
        try:
            self.current_period = period

            # Update button styles
            for item in self.children:
                if isinstance(item, Button):
                    if (period == ActivityPeriod.DAILY and "Daily" in item.label) or \
                       (period == ActivityPeriod.WEEKLY and "Weekly" in item.label) or \
                       (period == ActivityPeriod.MONTHLY and "Monthly" in item.label):
                        item.style = discord.ButtonStyle.primary
                    else:
                        item.style = discord.ButtonStyle.secondary

            # Get updated activity data
            activity_data = await self.tracker.get_member_activity(self.member, period)

            if not activity_data:
                embed = discord.Embed(
                    title="📊 No Activity Data",
                    description=f"No activity data found for {self.member.display_name} in the {period.value} period",
                    color=self.tracker.colors['neutral']
                )
            else:
                embed = await self._create_activity_embed(activity_data)

            await interaction.response.edit_message(embed=embed, view=self)

        except Exception as e:
            logger.error(f"Error updating activity view: {e}")
            embed = discord.Embed(
                title="❌ Error",
                description="Failed to update activity data",
                color=self.tracker.colors['error']
            )
            await interaction.response.edit_message(embed=embed, view=self)

    async def _create_activity_embed(self, data: MemberActivityData) -> discord.Embed:
        """Create comprehensive activity embed"""
        embed = discord.Embed(
            title=f"📊 Activity Report - {data.display_name}",
            description=f"Comprehensive activity data for the {self.current_period.value} period",
            color=self.tracker.colors['primary']
        )

        # Set member avatar as thumbnail
        if self.member.avatar:
            embed.set_thumbnail(url=self.member.avatar.url)

        # Summary statistics
        embed.add_field(
            name="📈 Summary Statistics",
            value=f"```"
                  f"Total Messages: {data.total_messages:,}\n"
                  f"Voice Time: {await self.tracker.format_duration(data.total_voice_time)}\n"
                  f"Active Hours: {data.total_active_hours:.1f}h\n"
                  f"Last Seen: {data.last_seen.strftime('%Y-%m-%d %H:%M UTC')}"
                  f"```",
            inline=False
        )

        # Voice channel activity
        if data.voice_sessions:
            voice_summary = defaultdict(int)
            for session in data.voice_sessions:
                voice_summary[session.channel_name] += session.duration_seconds

            voice_text = []
            for channel_name, total_time in sorted(voice_summary.items(),
                                                 key=lambda x: x[1], reverse=True)[:5]:
                duration_str = await self.tracker.format_duration(total_time)
                voice_text.append(f"• **{channel_name}**: {duration_str}")

            embed.add_field(
                name="🎤 Voice Activity (Top 5)",
                value="\n".join(voice_text) or "No voice activity",
                inline=True
            )

        # Message activity
        if data.message_activity:
            sorted_messages = sorted(data.message_activity,
                                   key=lambda x: x.message_count, reverse=True)[:5]

            message_text = []
            for activity in sorted_messages:
                message_text.append(f"• **{activity.channel_name}**: {activity.message_count:,} messages")

            embed.add_field(
                name="💬 Message Activity (Top 5)",
                value="\n".join(message_text) or "No message activity",
                inline=True
            )

        # Tracked roles
        if data.tracked_roles:
            role_names = []
            for role_id in data.tracked_roles:
                role = self.member.guild.get_role(role_id)
                if role:
                    role_names.append(role.name)

            embed.add_field(
                name="🏷️ Tracked Roles",
                value="\n".join([f"• {name}" for name in role_names]),
                inline=False
            )

        embed.set_footer(text=f"Period: {self.current_period.value.title()} | Use buttons to switch periods")
        return embed

class PersistentRoleDashboardView(View):
    """Persistent role-based activity dashboard that survives bot restarts"""

    def __init__(self, tracker: ActivityTracker, guild_id: int,
                 current_period: ActivityPeriod = ActivityPeriod.WEEKLY,
                 view_id: str = None):
        super().__init__(timeout=None)  # Persistent view
        self.tracker = tracker
        self.guild_id = guild_id
        self.current_period = current_period
        self.view_id = view_id or f"activity_dashboard_{guild_id}_{int(datetime.now().timestamp())}"
        self.selected_role_id = None
        self.current_page = 0
        self.members_per_page = 10
        self.role_members_data = []
        self.role_summary = {}

        # Add components with persistent custom_ids
        self._add_persistent_components()

    def _add_persistent_components(self):
        """Add persistent components with unique custom_ids"""
        # Role selection dropdown
        role_select = Select(
            placeholder="Select a role to view activity dashboard...",
            custom_id=f"{self.view_id}_role_select",
            options=[discord.SelectOption(label="Loading...", value="loading")]
        )
        role_select.callback = self._role_selected
        self.add_item(role_select)

        # Period buttons
        periods = [
            ("📅", "Daily", ActivityPeriod.DAILY),
            ("📊", "Weekly", ActivityPeriod.WEEKLY),
            ("📈", "Monthly", ActivityPeriod.MONTHLY)
        ]

        for emoji, label, period in periods:
            style = discord.ButtonStyle.primary if period == self.current_period else discord.ButtonStyle.secondary
            button = Button(
                label=label,
                emoji=emoji,
                style=style,
                custom_id=f"{self.view_id}_period_{period.value}"
            )
            button.callback = self._create_period_callback(period)
            self.add_item(button)

        # Pagination buttons
        pagination_buttons = [
            ("◀️ Previous", "prev_page", discord.ButtonStyle.secondary, True),
            ("Page 1/1", "page_info", discord.ButtonStyle.primary, True),
            ("Next ▶️", "next_page", discord.ButtonStyle.secondary, True),
            ("🔄 Refresh", "refresh_data", discord.ButtonStyle.success, False)
        ]

        for label, custom_id, style, disabled in pagination_buttons:
            button = Button(
                label=label,
                style=style,
                custom_id=f"{self.view_id}_{custom_id}",
                disabled=disabled
            )

            if custom_id == "prev_page":
                button.callback = self._previous_page
            elif custom_id == "page_info":
                button.callback = self._page_info
            elif custom_id == "next_page":
                button.callback = self._next_page
            elif custom_id == "refresh_data":
                button.callback = self._refresh_data

            self.add_item(button)

    async def _update_role_dropdown(self):
        """Update the role dropdown with current tracked roles"""
        try:
            guild = bot.get_guild(self.guild_id)
            if not guild:
                return

            options = []
            for role_id in self.tracker.tracked_roles:
                role = guild.get_role(role_id)
                if role:
                    member_count = len([m for m in role.members if not m.bot])
                    options.append(discord.SelectOption(
                        label=f"{role.name}",
                        description=f"{member_count} members",
                        value=str(role_id),
                        emoji="👥"
                    ))

            if not options:
                options = [discord.SelectOption(
                    label="No tracked roles configured",
                    value="none",
                    description="Use /activity_setup to configure roles"
                )]

            # Limit to 25 options (Discord limit)
            if len(options) > 25:
                options = options[:25]

            # Find and update the role select
            for item in self.children:
                if isinstance(item, Select) and item.custom_id.endswith("_role_select"):
                    item.options = options
                    break

        except Exception as e:
            logger.error(f"Error updating role dropdown: {e}")

    async def _create_dashboard_embed(self) -> discord.Embed:
        """Create the main dashboard embed for persistent view"""
        guild = bot.get_guild(self.guild_id)
        if not guild:
            return discord.Embed(
                title="❌ Error",
                description="Guild not found",
                color=self.tracker.colors['error']
            )

        if not self.selected_role_id or not self.role_summary:
            # Default embed when no role selected
            embed = discord.Embed(
                title="📊 Activity Dashboard",
                description="Select a tracked role from the dropdown menu to view comprehensive activity data for all members in that role.",
                color=self.tracker.colors['primary']
            )

            embed.add_field(
                name="🎯 Available Features",
                value="• **Role Overview**: Complete activity summary for the role\n"
                      "• **Member List**: Detailed activity data for each member\n"
                      "• **Time Periods**: Daily, weekly, and monthly views\n"
                      "• **Pagination**: Navigate through large member lists\n"
                      "• **Real-time Data**: Live activity tracking and updates",
                inline=False
            )

            tracked_roles_text = []
            for role_id in self.tracker.tracked_roles:
                role = guild.get_role(role_id)
                if role:
                    member_count = len([m for m in role.members if not m.bot])
                    tracked_roles_text.append(f"• **{role.name}**: {member_count} members")

            if tracked_roles_text:
                embed.add_field(
                    name="👥 Tracked Roles",
                    value="\n".join(tracked_roles_text[:10]),  # Limit to 10 for space
                    inline=False
                )

            embed.set_footer(text="Use the dropdown menu to select a role and view detailed activity data")
            return embed

        # Role-specific dashboard embed
        role_name = self.role_summary["role_name"]
        member_count = self.role_summary["member_count"]

        embed = discord.Embed(
            title=f"📊 {role_name} Activity Dashboard",
            description=f"Comprehensive activity overview for **{member_count}** members in the {self.current_period.value} period",
            color=self.tracker.colors['primary']
        )

        # Role summary statistics
        total_messages = self.role_summary["total_messages"]
        total_voice_time = self.role_summary["total_voice_time"]
        total_active_hours = self.role_summary["total_active_hours"]

        embed.add_field(
            name="📈 Role Summary",
            value=f"```"
                  f"Total Messages: {total_messages:,}\n"
                  f"Total Voice Time: {await self.tracker.format_duration(total_voice_time)}\n"
                  f"Total Active Hours: {total_active_hours:.1f}h\n"
                  f"Average per Member: {total_messages // max(member_count, 1):,} messages"
                  f"```",
            inline=False
        )

        # Member activity list (paginated)
        start_idx = self.current_page * self.members_per_page
        end_idx = start_idx + self.members_per_page
        page_members = self.role_members_data[start_idx:end_idx]

        if page_members:
            member_list = []
            for i, member_data in enumerate(page_members, start=start_idx + 1):
                voice_duration = await self.tracker.format_duration(member_data.total_voice_time)
                activity_score = member_data.total_messages + (member_data.total_voice_time // 60)

                member_list.append(
                    f"`{i:2d}.` **{member_data.display_name}**\n"
                    f"     💬 {member_data.total_messages:,} msgs | 🎤 {voice_duration} | 📊 {activity_score:,} pts"
                )

            embed.add_field(
                name=f"👥 Member Activity (Page {self.current_page + 1})",
                value="\n".join(member_list),
                inline=False
            )

        # Pagination info
        total_pages = max(1, (len(self.role_members_data) + self.members_per_page - 1) // self.members_per_page)
        embed.set_footer(
            text=f"Page {self.current_page + 1}/{total_pages} • "
                 f"{len(self.role_members_data)} total members • "
                 f"Period: {self.current_period.value.title()}"
        )

        return embed

    async def _load_role_data(self):
        """Load activity data for all members in the selected role (persistent version)"""
        try:
            if not self.selected_role_id:
                return

            guild = bot.get_guild(self.guild_id)
            if not guild:
                return

            role = guild.get_role(self.selected_role_id)
            if not role:
                return

            # Get all non-bot members with this role
            role_members = [m for m in role.members if not m.bot]

            # Load activity data for all members
            self.role_members_data = []
            total_messages = 0
            total_voice_time = 0
            total_active_hours = 0.0

            for member in role_members:
                activity_data = await self.tracker.get_member_activity(member, self.current_period)

                if activity_data:
                    self.role_members_data.append(activity_data)
                    total_messages += activity_data.total_messages
                    total_voice_time += activity_data.total_voice_time
                    total_active_hours += activity_data.total_active_hours
                else:
                    # Create empty data for members with no activity
                    empty_data = MemberActivityData(
                        user_id=member.id,
                        username=member.name,
                        display_name=member.display_name,
                        tracked_roles=[role.id],
                        total_messages=0,
                        total_voice_time=0,
                        total_active_hours=0.0,
                        voice_sessions=[],
                        message_activity=[],
                        last_seen=datetime.now(timezone.utc),
                        tracking_start=datetime.now(timezone.utc).date()
                    )
                    self.role_members_data.append(empty_data)

            # Sort by total activity (messages + voice time in minutes)
            self.role_members_data.sort(
                key=lambda x: x.total_messages + (x.total_voice_time // 60),
                reverse=True
            )

            # Calculate role summary
            self.role_summary = {
                "role_name": role.name,
                "member_count": len(self.role_members_data),
                "total_messages": total_messages,
                "total_voice_time": total_voice_time,
                "total_active_hours": total_active_hours,
                "period": self.current_period.value
            }

        except Exception as e:
            logger.error(f"Error loading role data: {e}")
            self.role_members_data = []
            self.role_summary = {}

    def _create_period_callback(self, period: ActivityPeriod):
        """Create callback for period buttons"""
        async def period_callback(interaction: discord.Interaction):
            await self._update_period(interaction, period)
        return period_callback

    async def _role_selected(self, interaction: discord.Interaction):
        """Handle role selection from dropdown (persistent version)"""
        try:
            await interaction.response.defer()

            selected_value = interaction.data["values"][0]
            if selected_value == "none" or selected_value == "loading":
                await interaction.followup.send(
                    "Please configure tracked roles using `/activity_setup` first.",
                    ephemeral=True
                )
                return

            self.selected_role_id = int(selected_value)
            self.current_page = 0

            # Load role data
            await self._load_role_data()

            # Update the view
            embed = await self._create_dashboard_embed()
            self._update_pagination_buttons()

            await interaction.edit_original_response(embed=embed, view=self)

        except Exception as e:
            logger.error(f"Error in role selection: {e}")
            await interaction.followup.send(
                "❌ Error loading role data. Please try again.",
                ephemeral=True
            )

    async def _update_period(self, interaction: discord.Interaction, period: ActivityPeriod):
        """Update the dashboard with new time period (persistent version)"""
        try:
            await interaction.response.defer()

            self.current_period = period
            self.current_page = 0

            # Update period button styles
            for item in self.children:
                if isinstance(item, Button) and item.custom_id and "period_" in item.custom_id:
                    if item.custom_id.endswith(f"_{period.value}"):
                        item.style = discord.ButtonStyle.primary
                    else:
                        item.style = discord.ButtonStyle.secondary

            # Reload data with new period
            if self.selected_role_id:
                await self._load_role_data()

            # Update the view
            embed = await self._create_dashboard_embed()
            self._update_pagination_buttons()

            await interaction.edit_original_response(embed=embed, view=self)

        except Exception as e:
            logger.error(f"Error updating period: {e}")
            await interaction.followup.send(
                "❌ Error updating time period. Please try again.",
                ephemeral=True
            )

    def _update_pagination_buttons(self):
        """Update pagination button states (persistent version)"""
        total_pages = max(1, (len(self.role_members_data) + self.members_per_page - 1) // self.members_per_page)

        # Find pagination buttons
        for item in self.children:
            if isinstance(item, Button):
                if item.custom_id.endswith("_prev_page"):
                    item.disabled = self.current_page <= 0
                elif item.custom_id.endswith("_page_info"):
                    item.label = f"Page {self.current_page + 1}/{total_pages}"
                    item.disabled = total_pages <= 1
                elif item.custom_id.endswith("_next_page"):
                    item.disabled = self.current_page >= total_pages - 1

    async def _previous_page(self, interaction: discord.Interaction):
        """Navigate to previous page (persistent version)"""
        try:
            await interaction.response.defer()

            if self.current_page > 0:
                self.current_page -= 1

                embed = await self._create_dashboard_embed()
                self._update_pagination_buttons()

                await interaction.edit_original_response(embed=embed, view=self)
            else:
                await interaction.followup.send("Already on the first page.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error navigating to previous page: {e}")

    async def _next_page(self, interaction: discord.Interaction):
        """Navigate to next page (persistent version)"""
        try:
            await interaction.response.defer()

            total_pages = max(1, (len(self.role_members_data) + self.members_per_page - 1) // self.members_per_page)

            if self.current_page < total_pages - 1:
                self.current_page += 1

                embed = await self._create_dashboard_embed()
                self._update_pagination_buttons()

                await interaction.edit_original_response(embed=embed, view=self)
            else:
                await interaction.followup.send("Already on the last page.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error navigating to next page: {e}")

    async def _page_info(self, interaction: discord.Interaction):
        """Show page information (persistent version)"""
        await interaction.response.defer()

    async def _refresh_data(self, interaction: discord.Interaction):
        """Refresh dashboard data (persistent version)"""
        try:
            await interaction.response.defer()

            if self.selected_role_id:
                await self._load_role_data()
                embed = await self._create_dashboard_embed()
                self._update_pagination_buttons()

                await interaction.edit_original_response(embed=embed, view=self)
            else:
                await interaction.followup.send("Please select a role first.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error refreshing data: {e}")
            await interaction.followup.send(
                "❌ Error refreshing data. Please try again.",
                ephemeral=True
            )

class RoleDashboardView(View):
    """Comprehensive role-based activity dashboard with pagination and filtering"""

    def __init__(self, tracker: ActivityTracker, guild: discord.Guild,
                 current_period: ActivityPeriod = ActivityPeriod.WEEKLY):
        super().__init__(timeout=600)  # 10 minutes for dashboard
        self.tracker = tracker
        self.guild = guild
        self.current_period = current_period
        self.selected_role_id = None
        self.current_page = 0
        self.members_per_page = 10
        self.role_members_data = []
        self.role_summary = {}

        # Add role selection dropdown
        self._add_role_dropdown()

        # Add period buttons
        self._add_period_buttons()

        # Add pagination buttons (initially disabled)
        self._add_pagination_buttons()

    def _add_role_dropdown(self):
        """Add dropdown for role selection"""
        options = []

        for role_id in self.tracker.tracked_roles:
            role = self.guild.get_role(role_id)
            if role:
                member_count = len([m for m in role.members if not m.bot])
                options.append(discord.SelectOption(
                    label=f"{role.name}",
                    description=f"{member_count} members",
                    value=str(role_id),
                    emoji="👥"
                ))

        if options:
            # Limit to 25 options (Discord limit)
            if len(options) > 25:
                options = options[:25]

            role_select = Select(
                placeholder="Select a role to view activity dashboard...",
                options=options,
                custom_id="role_select"
            )
            role_select.callback = self._role_selected
            self.add_item(role_select)

    def _add_period_buttons(self):
        """Add period selection buttons"""
        periods = [
            ("📅", "Daily", ActivityPeriod.DAILY),
            ("📊", "Weekly", ActivityPeriod.WEEKLY),
            ("📈", "Monthly", ActivityPeriod.MONTHLY)
        ]

        for emoji, label, period in periods:
            style = discord.ButtonStyle.primary if period == self.current_period else discord.ButtonStyle.secondary
            button = Button(
                label=label,
                emoji=emoji,
                style=style,
                custom_id=f"period_{period.value}"
            )
            button.callback = self._create_period_callback(period)
            self.add_item(button)

    def _add_pagination_buttons(self):
        """Add pagination control buttons"""
        # Previous page button
        prev_button = Button(
            label="◀️ Previous",
            style=discord.ButtonStyle.secondary,
            custom_id="prev_page",
            disabled=True
        )
        prev_button.callback = self._previous_page
        self.add_item(prev_button)

        # Page info button
        page_button = Button(
            label="Page 1/1",
            style=discord.ButtonStyle.primary,
            custom_id="page_info",
            disabled=True
        )
        page_button.callback = self._page_info
        self.add_item(page_button)

        # Next page button
        next_button = Button(
            label="Next ▶️",
            style=discord.ButtonStyle.secondary,
            custom_id="next_page",
            disabled=True
        )
        next_button.callback = self._next_page
        self.add_item(next_button)

        # Refresh button
        refresh_button = Button(
            label="🔄 Refresh",
            style=discord.ButtonStyle.success,
            custom_id="refresh_data"
        )
        refresh_button.callback = self._refresh_data
        self.add_item(refresh_button)

    def _create_period_callback(self, period: ActivityPeriod):
        """Create callback for period buttons"""
        async def period_callback(interaction: discord.Interaction):
            await self._update_period(interaction, period)
        return period_callback

    async def _role_selected(self, interaction: discord.Interaction):
        """Handle role selection from dropdown"""
        try:
            await interaction.response.defer()

            self.selected_role_id = int(interaction.data["values"][0])
            self.current_page = 0

            # Load role data
            await self._load_role_data()

            # Update the view
            embed = await self._create_dashboard_embed()
            self._update_pagination_buttons()

            await interaction.followup.edit_message(
                interaction.message.id,
                embed=embed,
                view=self
            )

        except Exception as e:
            logger.error(f"Error in role selection: {e}")
            await interaction.followup.send(
                "❌ Error loading role data. Please try again.",
                ephemeral=True
            )

    async def _load_role_data(self):
        """Load activity data for all members in the selected role"""
        try:
            if not self.selected_role_id:
                return

            role = self.guild.get_role(self.selected_role_id)
            if not role:
                return

            # Get all non-bot members with this role
            role_members = [m for m in role.members if not m.bot]

            # Load activity data for all members
            self.role_members_data = []
            total_messages = 0
            total_voice_time = 0
            total_active_hours = 0.0

            for member in role_members:
                activity_data = await self.tracker.get_member_activity(member, self.current_period)

                if activity_data:
                    self.role_members_data.append(activity_data)
                    total_messages += activity_data.total_messages
                    total_voice_time += activity_data.total_voice_time
                    total_active_hours += activity_data.total_active_hours
                else:
                    # Create empty data for members with no activity
                    empty_data = MemberActivityData(
                        user_id=member.id,
                        username=member.name,
                        display_name=member.display_name,
                        tracked_roles=[role.id],
                        total_messages=0,
                        total_voice_time=0,
                        total_active_hours=0.0,
                        voice_sessions=[],
                        message_activity=[],
                        last_seen=datetime.now(timezone.utc),
                        tracking_start=datetime.now(timezone.utc).date()
                    )
                    self.role_members_data.append(empty_data)

            # Sort by total activity (messages + voice time in minutes)
            self.role_members_data.sort(
                key=lambda x: x.total_messages + (x.total_voice_time // 60),
                reverse=True
            )

            # Calculate role summary
            self.role_summary = {
                "role_name": role.name,
                "member_count": len(self.role_members_data),
                "total_messages": total_messages,
                "total_voice_time": total_voice_time,
                "total_active_hours": total_active_hours,
                "period": self.current_period.value
            }

        except Exception as e:
            logger.error(f"Error loading role data: {e}")
            self.role_members_data = []
            self.role_summary = {}

    async def _create_dashboard_embed(self) -> discord.Embed:
        """Create the main dashboard embed"""
        if not self.selected_role_id or not self.role_summary:
            # Default embed when no role selected
            embed = discord.Embed(
                title="📊 Activity Dashboard",
                description="Select a tracked role from the dropdown menu to view comprehensive activity data for all members in that role.",
                color=self.tracker.colors['primary']
            )

            embed.add_field(
                name="🎯 Available Features",
                value="• **Role Overview**: Complete activity summary for the role\n"
                      "• **Member List**: Detailed activity data for each member\n"
                      "• **Time Periods**: Daily, weekly, and monthly views\n"
                      "• **Pagination**: Navigate through large member lists\n"
                      "• **Real-time Data**: Live activity tracking and updates",
                inline=False
            )

            tracked_roles_text = []
            for role_id in self.tracker.tracked_roles:
                role = self.guild.get_role(role_id)
                if role:
                    member_count = len([m for m in role.members if not m.bot])
                    tracked_roles_text.append(f"• **{role.name}**: {member_count} members")

            if tracked_roles_text:
                embed.add_field(
                    name="👥 Tracked Roles",
                    value="\n".join(tracked_roles_text[:10]),  # Limit to 10 for space
                    inline=False
                )

            embed.set_footer(text="Use the dropdown menu to select a role and view detailed activity data")
            return embed

        # Role-specific dashboard embed
        role_name = self.role_summary["role_name"]
        member_count = self.role_summary["member_count"]

        embed = discord.Embed(
            title=f"📊 {role_name} Activity Dashboard",
            description=f"Comprehensive activity overview for **{member_count}** members in the {self.current_period.value} period",
            color=self.tracker.colors['primary']
        )

        # Role summary statistics
        total_messages = self.role_summary["total_messages"]
        total_voice_time = self.role_summary["total_voice_time"]
        total_active_hours = self.role_summary["total_active_hours"]

        embed.add_field(
            name="📈 Role Summary",
            value=f"```"
                  f"Total Messages: {total_messages:,}\n"
                  f"Total Voice Time: {await self.tracker.format_duration(total_voice_time)}\n"
                  f"Total Active Hours: {total_active_hours:.1f}h\n"
                  f"Average per Member: {total_messages // max(member_count, 1):,} messages"
                  f"```",
            inline=False
        )

        # Member activity list (paginated)
        start_idx = self.current_page * self.members_per_page
        end_idx = start_idx + self.members_per_page
        page_members = self.role_members_data[start_idx:end_idx]

        if page_members:
            member_list = []
            for i, member_data in enumerate(page_members, start=start_idx + 1):
                voice_duration = await self.tracker.format_duration(member_data.total_voice_time)
                activity_score = member_data.total_messages + (member_data.total_voice_time // 60)

                member_list.append(
                    f"`{i:2d}.` **{member_data.display_name}**\n"
                    f"     💬 {member_data.total_messages:,} msgs | 🎤 {voice_duration} | 📊 {activity_score:,} pts"
                )

            embed.add_field(
                name=f"👥 Member Activity (Page {self.current_page + 1})",
                value="\n".join(member_list),
                inline=False
            )

        # Pagination info
        total_pages = max(1, (len(self.role_members_data) + self.members_per_page - 1) // self.members_per_page)
        embed.set_footer(
            text=f"Page {self.current_page + 1}/{total_pages} • "
                 f"{len(self.role_members_data)} total members • "
                 f"Period: {self.current_period.value.title()}"
        )

        return embed

    def _update_pagination_buttons(self):
        """Update pagination button states"""
        total_pages = max(1, (len(self.role_members_data) + self.members_per_page - 1) // self.members_per_page)

        # Find pagination buttons
        prev_button = None
        page_button = None
        next_button = None

        for item in self.children:
            if isinstance(item, Button):
                if item.custom_id == "prev_page":
                    prev_button = item
                elif item.custom_id == "page_info":
                    page_button = item
                elif item.custom_id == "next_page":
                    next_button = item

        # Update button states
        if prev_button:
            prev_button.disabled = self.current_page <= 0

        if page_button:
            page_button.label = f"Page {self.current_page + 1}/{total_pages}"
            page_button.disabled = total_pages <= 1

        if next_button:
            next_button.disabled = self.current_page >= total_pages - 1

    async def _update_period(self, interaction: discord.Interaction, period: ActivityPeriod):
        """Update the dashboard with new time period"""
        try:
            await interaction.response.defer()

            self.current_period = period
            self.current_page = 0

            # Update period button styles
            for item in self.children:
                if isinstance(item, Button) and item.custom_id and item.custom_id.startswith("period_"):
                    if item.custom_id == f"period_{period.value}":
                        item.style = discord.ButtonStyle.primary
                    else:
                        item.style = discord.ButtonStyle.secondary

            # Reload data with new period
            if self.selected_role_id:
                await self._load_role_data()

            # Update the view
            embed = await self._create_dashboard_embed()
            self._update_pagination_buttons()

            await interaction.followup.edit_message(
                interaction.message.id,
                embed=embed,
                view=self
            )

        except Exception as e:
            logger.error(f"Error updating period: {e}")
            await interaction.followup.send(
                "❌ Error updating time period. Please try again.",
                ephemeral=True
            )

    async def _previous_page(self, interaction: discord.Interaction):
        """Navigate to previous page"""
        try:
            await interaction.response.defer()

            if self.current_page > 0:
                self.current_page -= 1

                embed = await self._create_dashboard_embed()
                self._update_pagination_buttons()

                await interaction.followup.edit_message(
                    interaction.message.id,
                    embed=embed,
                    view=self
                )
            else:
                await interaction.followup.send(
                    "Already on the first page.",
                    ephemeral=True
                )

        except Exception as e:
            logger.error(f"Error navigating to previous page: {e}")

    async def _next_page(self, interaction: discord.Interaction):
        """Navigate to next page"""
        try:
            await interaction.response.defer()

            total_pages = max(1, (len(self.role_members_data) + self.members_per_page - 1) // self.members_per_page)

            if self.current_page < total_pages - 1:
                self.current_page += 1

                embed = await self._create_dashboard_embed()
                self._update_pagination_buttons()

                await interaction.followup.edit_message(
                    interaction.message.id,
                    embed=embed,
                    view=self
                )
            else:
                await interaction.followup.send(
                    "Already on the last page.",
                    ephemeral=True
                )

        except Exception as e:
            logger.error(f"Error navigating to next page: {e}")

    async def _page_info(self, interaction: discord.Interaction):
        """Show page information"""
        await interaction.response.defer()

    async def _refresh_data(self, interaction: discord.Interaction):
        """Refresh dashboard data"""
        try:
            await interaction.response.defer()

            if self.selected_role_id:
                await self._load_role_data()
                embed = await self._create_dashboard_embed()
                self._update_pagination_buttons()

                await interaction.followup.edit_message(
                    interaction.message.id,
                    embed=embed,
                    view=self
                )
            else:
                await interaction.followup.send(
                    "Please select a role first.",
                    ephemeral=True
                )

        except Exception as e:
            logger.error(f"Error refreshing data: {e}")
            await interaction.followup.send(
                "❌ Error refreshing data. Please try again.",
                ephemeral=True
            )

# Slash Commands
@bot.tree.command(name="activity_setup", description="Configure activity tracking system")
@app_commands.default_permissions(administrator=True)
async def activity_setup(interaction: discord.Interaction):
    """Setup command for activity tracking configuration"""
    try:
        if not activity_tracker.is_initialized:
            await activity_tracker.initialize()

        embed = discord.Embed(
            title="🔧 Activity Tracker Setup",
            description="Configure the member activity tracking system using the buttons below.\n\n"
                       "**Features:**\n"
                       "• Track message counts per channel\n"
                       "• Monitor voice channel activity\n"
                       "• Calculate active hours\n"
                       "• Role-based access control\n"
                       "• Professional data presentation",
            color=activity_tracker.colors['primary']
        )

        embed.add_field(
            name="📋 Setup Steps",
            value="1. **Configure Tracked Roles** - Select which roles to monitor\n"
                  "2. **Configure Access Roles** - Set who can view activity data\n"
                  "3. **View Configuration** - Review current settings",
            inline=False
        )

        embed.set_footer(text="Only users with configured access roles can view activity data")

        view = ActivitySetupView(activity_tracker)
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in activity setup command: {e}")
        embed = discord.Embed(
            title="❌ Setup Error",
            description="Failed to initialize activity tracking setup",
            color=activity_tracker.colors['error']
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="activity_search", description="Access comprehensive role-based activity dashboard")
async def activity_search(interaction: discord.Interaction):
    """Role-based activity dashboard with comprehensive member oversight"""
    try:
        if not activity_tracker.is_initialized:
            await activity_tracker.initialize()

        # Strict permission check - only configured roles have access
        if not await activity_tracker.has_access_permission(interaction.user):
            embed = discord.Embed(
                title="🔐 Access Denied",
                description="You do not have permission to access activity data.\n"
                           "Contact an administrator to configure your access.",
                color=activity_tracker.colors['error']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Check if there are any tracked roles
        if not activity_tracker.tracked_roles:
            embed = discord.Embed(
                title="⚙️ No Tracked Roles",
                description="No roles are currently configured for tracking.\n"
                           "Use `/activity_setup` to configure tracked roles first.",
                color=activity_tracker.colors['warning']
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Defer response for dashboard creation
        await interaction.response.defer(ephemeral=True)

        # Create the persistent role dashboard
        dashboard = PersistentRoleDashboardView(activity_tracker, interaction.guild.id)

        # Update role dropdown with current data
        await dashboard._update_role_dropdown()

        # Create initial embed
        embed = await dashboard._create_dashboard_embed()

        # Send the dashboard
        message = await interaction.followup.send(embed=embed, view=dashboard, ephemeral=True)

        # Save persistent dashboard info
        await activity_tracker.save_persistent_dashboard(
            dashboard.view_id,
            interaction.guild.id,
            interaction.channel.id,
            message.id
        )

    except Exception as e:
        logger.error(f"Error in activity dashboard command: {e}")
        embed = discord.Embed(
            title="❌ Dashboard Error",
            description="Failed to create activity dashboard",
            color=activity_tracker.colors['error']
        )

        if interaction.response.is_done():
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            await interaction.response.send_message(embed=embed, ephemeral=True)

# Event handlers are integrated into the main bot.py file
# to avoid conflicts with existing handlers

# Global activity tracker instance
activity_tracker = ActivityTracker()
