# Discord Activity Tracker System

## Overview

The Discord Activity Tracker System is an advanced monitoring solution that provides comprehensive activity tracking for Discord server members with specific roles. It offers real-time tracking of messages, voice channel activity, and presence data with professional data presentation and strict access controls.

## Features

### Core Functionality
- **Message Tracking**: Real-time monitoring of text messages per channel with precise timestamps
- **Voice Activity**: Detailed tracking of voice channel join/leave times and duration calculations
- **Presence Monitoring**: Active hours calculation based on member presence and activity
- **Role-Based Filtering**: Selective tracking of members with configured roles only
- **Time Period Analysis**: Daily, weekly, and monthly activity views with easy switching

### Technical Features
- **High Performance**: Optimized for large-scale servers with thousands of members
- **Database Efficiency**: MongoDB-based storage with comprehensive indexing
- **Real-Time Processing**: Live activity tracking without performance degradation
- **Batch Operations**: Efficient cache management and bulk database updates
- **Error Handling**: Robust error recovery and logging systems

### User Interface
- **Professional Embeds**: Clean, modern Discord embed design with dark theme
- **Interactive Controls**: Button-based navigation and period switching
- **Detailed Breakdowns**: Per-channel statistics and comprehensive summaries
- **Access Control**: Strict role-based permissions with admin override protection

## Installation and Setup

### Prerequisites
- Python 3.8+
- Discord.py 2.0+
- MongoDB database
- Existing Discord bot with proper intents

### Required Intents
```python
intents = discord.Intents.default()
intents.members = True
intents.presences = True
intents.message_content = True
```

### Integration Steps

1. **Import the Module**
   ```python
   from activity_tracker import activity_tracker
   ```

2. **Initialize in Bot Startup**
   ```python
   @bot.event
   async def on_ready():
       await activity_tracker.initialize()
   ```

3. **Event Handler Integration**
   The system automatically integrates with existing event handlers in `bot.py`.

## Configuration

### Setup Command
Use the `/activity_setup` command (administrator only) to configure the system:

1. **Configure Tracked Roles**: Select which roles should be monitored
2. **Configure Access Roles**: Set which roles can view activity data
3. **View Configuration**: Review current settings

### Role Configuration
- **Tracked Roles**: Members with these roles will have their activity monitored
- **Access Roles**: Members with these roles can view activity data
- **Admin Override**: Administrators do NOT have automatic access unless they have configured access roles

## Usage

### Viewing Activity Data
Use the `/activity_search` command to access the comprehensive role-based dashboard:

#### Dashboard Features
- **Role Selection**: Dropdown menu to select from all tracked roles
- **Member Overview**: View all members in the selected role with pagination
- **Activity Summary**: Complete statistics for the entire role
- **Period Filtering**: Switch between daily, weekly, and monthly views
- **Real-Time Updates**: Refresh data with live activity tracking

#### Dashboard Interface
1. **Role Dropdown**: Select any tracked role to view its members
2. **Summary Statistics**: Total messages, voice time, and averages for the role
3. **Member List**: Paginated list showing each member's activity with scores
4. **Navigation Controls**: Period buttons, pagination, and refresh functionality
5. **Persistence**: Dashboard survives bot restarts and maintains state

### Interactive Features
- **Role-Based View**: Monitor entire role groups instead of individual members
- **Pagination**: Navigate through large member lists (20+ members supported)
- **Period Switching**: Use buttons to switch between daily, weekly, and monthly views
- **Real-Time Updates**: Data refreshes automatically with latest activity
- **Persistent Interface**: Dashboard components restore after bot restarts
- **Detailed Breakdowns**: View per-channel statistics and comprehensive summaries

## Data Structure

### Database Collections
- **member_activity**: Stores daily activity records for each member
- **activity_config**: Stores system configuration (tracked roles, access roles)

### Activity Record Schema
```javascript
{
  "_id": ObjectId,
  "user_id": 123456789,
  "guild_id": 987654321,
  "date": "2024-01-15",
  "total_messages": 45,
  "total_voice_time": 3600,
  "voice_sessions": [
    {
      "channel_id": 111111111,
      "channel_name": "General Voice",
      "start_time": ISODate,
      "end_time": ISODate,
      "duration_seconds": 1800
    }
  ],
  "message_activity": {
    "222222222": {
      "channel_name": "general",
      "message_count": 25,
      "last_message_time": ISODate
    }
  },
  "last_updated": ISODate
}
```

## Performance Optimization

### Caching System
- **Message Cache**: Batches message counts before database writes
- **Presence Cache**: Tracks recent presence updates to avoid spam
- **Flush Intervals**: Automatic cache flushing every 5 minutes

### Database Indexing
- Compound indexes on user_id + guild_id + date
- Channel-specific indexes for voice and message queries
- Optimized for time-range queries and aggregations

### Memory Management
- Efficient data structures for real-time tracking
- Automatic cleanup of completed voice sessions
- Configurable cache sizes and flush intervals

## Security and Access Control

### Permission System
- **Strict Role Checking**: Only configured access roles can view data
- **No Admin Override**: Administrators must have configured access roles
- **Ephemeral Responses**: All activity data shown privately to requesters

### Data Privacy
- **Role-Based Tracking**: Only members with tracked roles are monitored
- **Secure Storage**: All data encrypted in MongoDB
- **Access Logging**: All data access attempts are logged

## API Reference

### Main Classes

#### ActivityTracker
Main tracking system class with the following key methods:
- `initialize()`: Initialize the tracker with database connections
- `track_message(message)`: Track a message for activity monitoring
- `track_voice_state_update(member, before, after)`: Track voice state changes
- `get_member_activity(member, period)`: Retrieve activity data for a member

#### ActivityPeriod (Enum)
- `DAILY`: Daily activity view
- `WEEKLY`: Weekly activity view (default)
- `MONTHLY`: Monthly activity view

#### Data Classes
- `VoiceSession`: Represents a voice channel session
- `MessageActivity`: Represents message activity in a channel
- `MemberActivityData`: Complete activity data for a member

### Discord Commands

#### /activity_setup
- **Description**: Configure activity tracking system
- **Permissions**: Administrator only
- **Usage**: Interactive setup with buttons and modals

#### /activity_search
- **Description**: Access comprehensive role-based activity dashboard
- **Parameters**: None (interactive dashboard interface)
- **Features**:
  - Role selection dropdown with member counts
  - Paginated member lists (10 members per page)
  - Period switching (daily/weekly/monthly)
  - Real-time data refresh
  - Persistent interface across bot restarts
- **Permissions**: Configured access roles only

## Dashboard System

### Role-Based Dashboard
The activity tracker features a comprehensive role-based dashboard that provides oversight of entire role groups rather than individual members.

#### Dashboard Components
1. **Role Selection Dropdown**
   - Lists all tracked roles with member counts
   - Supports up to 25 roles (Discord limit)
   - Shows real-time member counts

2. **Role Summary Statistics**
   - Total messages across all role members
   - Combined voice time for the role
   - Average activity per member
   - Period-specific calculations

3. **Member Activity List**
   - Paginated display (10 members per page)
   - Activity scoring system (messages + voice minutes)
   - Sorted by total activity (most active first)
   - Per-member breakdown with messages, voice time, and scores

4. **Interactive Controls**
   - Period switching buttons (Daily/Weekly/Monthly)
   - Pagination controls (Previous/Next/Page Info)
   - Data refresh button for real-time updates
   - Persistent state across interactions

#### Pagination System
- **Efficient Handling**: Supports roles with 20+ members
- **Page Navigation**: Previous/Next buttons with page indicators
- **Smart Pagination**: Automatic button state management
- **Performance Optimized**: Loads data in batches for large roles

#### Persistence Features
- **Bot Restart Survival**: Dashboard components restore automatically
- **State Maintenance**: Preserves user selections and current page
- **Database Tracking**: Persistent view information stored in MongoDB
- **Automatic Cleanup**: Old dashboard entries cleaned up automatically

### Dashboard Usage Workflow
1. **Access**: Use `/activity_search` command (requires access role)
2. **Select Role**: Choose from dropdown menu of tracked roles
3. **View Data**: Browse paginated member list with activity scores
4. **Navigate**: Use period buttons and pagination controls
5. **Refresh**: Update data in real-time with refresh button

## Troubleshooting

### Common Issues

1. **No Data Showing**
   - Verify member has tracked roles
   - Check if tracking period includes recent activity
   - Ensure database connection is working

2. **Access Denied**
   - Verify user has configured access roles
   - Check role configuration in setup
   - Administrators need access roles too

3. **Performance Issues**
   - Monitor database connection
   - Check cache flush intervals
   - Review server member count vs. tracked roles

### Logging
The system provides comprehensive logging:
- Initialization status
- Database operations
- Error conditions
- Performance metrics

### Database Maintenance
- Regular index optimization
- Periodic data cleanup for old records
- Monitor collection sizes and performance

## Best Practices

### Configuration
- Limit tracked roles to essential roles only
- Configure access roles carefully
- Regular review of role assignments

### Performance
- Monitor database performance regularly
- Adjust cache intervals based on server size
- Use time-based data retention policies

### Security
- Regular access role audits
- Monitor data access patterns
- Implement data retention policies

## Support and Maintenance

### Regular Tasks
- Database performance monitoring
- Index optimization
- Log file management
- Configuration reviews

### Updates and Patches
- Monitor for Discord.py updates
- Database schema migrations
- Performance optimizations
- Security patches

For technical support or feature requests, refer to the bot's main documentation or contact the development team.
