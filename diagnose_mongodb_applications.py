#!/usr/bin/env python3
"""
Diagnostic script to check MongoDB for application data.
This will help identify why the restoration process is failing.
"""

import pymongo
import json
from datetime import datetime

def check_mongodb_connection():
    """Check if MongoDB is accessible"""
    try:
        client = pymongo.MongoClient("mongodb://localhost:27017/", serverSelectionTimeoutMS=5000)
        # Test connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        return client
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return None

def check_database_and_collections(client):
    """Check if the database and collections exist"""
    try:
        db = client["missminutesbot"]
        collections = db.list_collection_names()
        print(f"📋 Database 'missminutesbot' collections: {collections}")
        
        if "applications" in collections:
            print("✅ 'applications' collection exists")
            return db
        else:
            print("❌ 'applications' collection not found")
            return db
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return None

def analyze_applications_collection(db):
    """Analyze the applications collection"""
    try:
        collection = db["applications"]
        
        # Get all documents in the collection
        documents = list(collection.find())
        print(f"📊 Applications collection contains {len(documents)} documents")
        
        if not documents:
            print("❌ Applications collection is empty!")
            return
        
        # Check for the main applications document
        main_doc = collection.find_one({"_id": "applications"})
        if main_doc:
            print("✅ Main applications document found")
            
            # Analyze the structure
            print("\n📋 Document structure:")
            for key in main_doc.keys():
                if key != "_id":
                    value = main_doc[key]
                    if isinstance(value, dict):
                        print(f"   {key}: dict with {len(value)} items")
                        if key == "status":
                            print("   📝 Application status entries:")
                            for user_id, status in value.items():
                                message_id = status.get("message_id", "No message_id")
                                app_name = status.get("application_name", "Unknown")
                                responded = status.get("responded", False)
                                print(f"      User {user_id}: {app_name} (Message: {message_id}, Responded: {responded})")
                    else:
                        print(f"   {key}: {type(value).__name__}")
            
            # Check for the specific application we're looking for
            status_data = main_doc.get("status", {})
            target_user = "567939816721874957"
            target_message = "1403563563695472752"
            
            print(f"\n🔍 Looking for user {target_user} with message {target_message}:")
            
            if target_user in status_data:
                user_status = status_data[target_user]
                print(f"✅ Found user {target_user} in database:")
                print(f"   Message ID: {user_status.get('message_id', 'Not set')}")
                print(f"   Application: {user_status.get('application_name', 'Unknown')}")
                print(f"   Responded: {user_status.get('responded', False)}")
                print(f"   Submission time: {user_status.get('submission_time', 'Unknown')}")
                
                # Check if this should be restored
                has_message_id = bool(user_status.get("message_id"))
                is_responded = user_status.get("responded", False)
                
                if has_message_id and not is_responded:
                    print("✅ This application SHOULD be restored by the bot")
                    expected_key = f"{target_user}_{user_status.get('message_id')}"
                    print(f"   Expected registry key: {expected_key}")
                else:
                    print("❌ This application should NOT be restored:")
                    if not has_message_id:
                        print("     - No message_id")
                    if is_responded:
                        print("     - Already responded")
            else:
                print(f"❌ User {target_user} not found in database")
                print("   Available users:")
                for user_id in status_data.keys():
                    print(f"     - {user_id}")
        else:
            print("❌ Main applications document not found")
            print("   Available documents:")
            for doc in documents:
                print(f"     - {doc.get('_id', 'No ID')}")
                
    except Exception as e:
        print(f"❌ Error analyzing applications collection: {e}")

def check_application_channels(db):
    """Check if application channels are configured"""
    try:
        collection = db["applications"]
        main_doc = collection.find_one({"_id": "applications"})
        
        if not main_doc:
            print("❌ No applications document found for channel check")
            return
        
        print("\n📺 Application Channel Configuration:")
        
        # Check for channels in the document
        channels = main_doc.get("channels", {})
        if channels:
            print("✅ Channels configuration found (new structure):")
            for key, value in channels.items():
                print(f"   {key}: {value}")
        else:
            # Check old structure
            app_channel = main_doc.get("channel")
            log_channel = main_doc.get("log_channel")
            
            if app_channel or log_channel:
                print("✅ Channels configuration found (old structure):")
                print(f"   application_channel: {app_channel}")
                print(f"   log_channel: {log_channel}")
            else:
                print("❌ No channel configuration found")
                
    except Exception as e:
        print(f"❌ Error checking application channels: {e}")

def main():
    """Run all diagnostics"""
    print("🔍 MongoDB Application Data Diagnostic")
    print("=" * 60)
    
    # Check MongoDB connection
    client = check_mongodb_connection()
    if not client:
        print("\n🔧 Troubleshooting steps:")
        print("   1. Ensure MongoDB is running: net start MongoDB")
        print("   2. Check MongoDB service status")
        print("   3. Verify MongoDB is listening on localhost:27017")
        return
    
    # Check database and collections
    db = check_database_and_collections(client)
    if db is None:
        return
    
    # Analyze applications data
    analyze_applications_collection(db)
    
    # Check channel configuration
    check_application_channels(db)
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("If the user and message are found in MongoDB but the registry is empty,")
    print("then the restoration process is failing to:")
    print("   1. Load the data from MongoDB")
    print("   2. Find the application log channel")
    print("   3. Fetch the message from Discord")
    print("   4. Create and register the view")
    print("\nCheck the bot startup logs for specific error messages during restoration.")

if __name__ == "__main__":
    main()
