"""
Test script for the Discord Activity Tracker System

This script provides comprehensive testing for the activity tracking functionality
including database operations, event handling, and UI components.
"""

import asyncio
import unittest
from unittest.mock import Mock, AsyncMock, patch
import discord
from datetime import datetime, timezone, timedelta
import sys
import os

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from activity_tracker import (
    ActivityTracker, ActivityPeriod, VoiceSession, MessageActivity, 
    MemberActivityData, ActivitySetupView, ActivityDisplayView
)

class TestActivityTracker(unittest.TestCase):
    """Test cases for the ActivityTracker class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tracker = ActivityTracker()
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 12345
        self.mock_member = Mock(spec=discord.Member)
        self.mock_member.id = 67890
        self.mock_member.name = "TestUser"
        self.mock_member.display_name = "Test User"
        self.mock_member.guild = self.mock_guild
        self.mock_member.roles = []
        
    def test_activity_period_enum(self):
        """Test ActivityPeriod enum values"""
        self.assertEqual(ActivityPeriod.DAILY.value, "daily")
        self.assertEqual(ActivityPeriod.WEEKLY.value, "weekly")
        self.assertEqual(ActivityPeriod.MONTHLY.value, "monthly")
    
    def test_voice_session_creation(self):
        """Test VoiceSession dataclass creation"""
        start_time = datetime.now(timezone.utc)
        session = VoiceSession(
            channel_id=123,
            channel_name="General Voice",
            start_time=start_time
        )
        
        self.assertEqual(session.channel_id, 123)
        self.assertEqual(session.channel_name, "General Voice")
        self.assertEqual(session.start_time, start_time)
        self.assertIsNone(session.end_time)
        self.assertEqual(session.duration_seconds, 0)
    
    def test_message_activity_creation(self):
        """Test MessageActivity dataclass creation"""
        last_time = datetime.now(timezone.utc)
        activity = MessageActivity(
            channel_id=456,
            channel_name="general",
            message_count=10,
            last_message_time=last_time
        )
        
        self.assertEqual(activity.channel_id, 456)
        self.assertEqual(activity.channel_name, "general")
        self.assertEqual(activity.message_count, 10)
        self.assertEqual(activity.last_message_time, last_time)
    
    def test_member_activity_data_creation(self):
        """Test MemberActivityData dataclass creation"""
        now = datetime.now(timezone.utc)
        data = MemberActivityData(
            user_id=67890,
            username="testuser",
            display_name="Test User",
            tracked_roles=[1, 2, 3],
            total_messages=100,
            total_voice_time=3600,
            total_active_hours=5.5,
            voice_sessions=[],
            message_activity=[],
            last_seen=now,
            tracking_start=now.date()
        )
        
        self.assertEqual(data.user_id, 67890)
        self.assertEqual(data.username, "testuser")
        self.assertEqual(data.total_messages, 100)
        self.assertEqual(data.total_voice_time, 3600)
        self.assertEqual(data.total_active_hours, 5.5)
    
    @patch('activity_tracker.db_manager')
    async def test_tracker_initialization(self, mock_db_manager):
        """Test tracker initialization"""
        mock_db_manager.is_connected = True
        mock_db_manager.db = Mock()
        mock_collection = AsyncMock()
        mock_db_manager.db.__getitem__ = Mock(return_value=mock_collection)
        
        # Mock the create_index method
        mock_collection.create_index = AsyncMock()
        
        # Mock the find_one method for config loading
        mock_collection.find_one = AsyncMock(return_value=None)
        
        # Mock the replace_one method for config saving
        mock_collection.replace_one = AsyncMock()
        
        await self.tracker.initialize()
        
        self.assertTrue(self.tracker.is_initialized)
        self.assertIsNotNone(self.tracker.activity_collection)
        self.assertIsNotNone(self.tracker.config_collection)
    
    async def test_is_member_tracked(self):
        """Test member tracking logic"""
        # Set up tracked roles
        self.tracker.tracked_roles = {1, 2, 3}
        
        # Create mock roles
        role1 = Mock(spec=discord.Role)
        role1.id = 1
        role2 = Mock(spec=discord.Role)
        role2.id = 4
        
        # Test member with tracked role
        self.mock_member.roles = [role1]
        result = await self.tracker.is_member_tracked(self.mock_member)
        self.assertTrue(result)
        
        # Test member without tracked role
        self.mock_member.roles = [role2]
        result = await self.tracker.is_member_tracked(self.mock_member)
        self.assertFalse(result)
        
        # Test with no tracked roles configured
        self.tracker.tracked_roles = set()
        result = await self.tracker.is_member_tracked(self.mock_member)
        self.assertFalse(result)
    
    async def test_has_access_permission(self):
        """Test access permission logic"""
        # Set up authorized roles
        self.tracker.authorized_roles = {5, 6, 7}
        
        # Create mock roles
        role5 = Mock(spec=discord.Role)
        role5.id = 5
        role8 = Mock(spec=discord.Role)
        role8.id = 8
        
        # Test member with access role
        self.mock_member.roles = [role5]
        result = await self.tracker.has_access_permission(self.mock_member)
        self.assertTrue(result)
        
        # Test member without access role
        self.mock_member.roles = [role8]
        result = await self.tracker.has_access_permission(self.mock_member)
        self.assertFalse(result)
    
    async def test_format_duration(self):
        """Test duration formatting"""
        # Test seconds
        result = await self.tracker.format_duration(45)
        self.assertEqual(result, "45s")
        
        # Test minutes
        result = await self.tracker.format_duration(125)
        self.assertEqual(result, "2m 5s")
        
        # Test hours
        result = await self.tracker.format_duration(3665)
        self.assertEqual(result, "1h 1m")
        
        # Test exact hour
        result = await self.tracker.format_duration(3600)
        self.assertEqual(result, "1h 0m")

class TestActivityViews(unittest.TestCase):
    """Test cases for Discord UI views"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tracker = ActivityTracker()
        self.mock_member = Mock(spec=discord.Member)
        self.mock_member.id = 67890
        self.mock_member.display_name = "Test User"
        self.mock_member.avatar = None
        
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 12345
        self.mock_member.guild = self.mock_guild
    
    def test_activity_setup_view_creation(self):
        """Test ActivitySetupView creation"""
        view = ActivitySetupView(self.tracker)
        self.assertEqual(view.tracker, self.tracker)
        self.assertEqual(view.timeout, 300)
        
        # Check that buttons are added
        self.assertEqual(len(view.children), 3)
    
    def test_activity_display_view_creation(self):
        """Test ActivityDisplayView creation"""
        view = ActivityDisplayView(self.tracker, self.mock_member)
        self.assertEqual(view.tracker, self.tracker)
        self.assertEqual(view.member, self.mock_member)
        self.assertEqual(view.current_period, ActivityPeriod.WEEKLY)
        self.assertEqual(view.timeout, 300)
        
        # Check that buttons are added
        self.assertEqual(len(view.children), 3)

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.tracker = ActivityTracker()
    
    @patch('activity_tracker.bot')
    async def test_message_tracking_flow(self, mock_bot):
        """Test the complete message tracking flow"""
        # Set up mocks
        mock_message = Mock(spec=discord.Message)
        mock_message.author = Mock(spec=discord.Member)
        mock_message.author.id = 12345
        mock_message.channel = Mock(spec=discord.TextChannel)
        mock_message.channel.id = 67890
        mock_message.guild = Mock(spec=discord.Guild)
        
        # Initialize tracker state
        self.tracker.is_initialized = True
        self.tracker.tracked_roles = {1}
        
        # Mock member roles
        role = Mock(spec=discord.Role)
        role.id = 1
        mock_message.author.roles = [role]
        
        # Track the message
        await self.tracker.track_message(mock_message)
        
        # Verify message was cached
        self.assertEqual(
            self.tracker.message_cache[mock_message.author.id][mock_message.channel.id], 
            1
        )
        
        # Verify presence was updated
        self.assertIn(mock_message.author.id, self.tracker.presence_cache)

def run_async_test(coro):
    """Helper function to run async tests"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

if __name__ == "__main__":
    # Run the tests
    print("Running Activity Tracker Tests...")

    # Create test loader
    loader = unittest.TestLoader()

    # Create test suite
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestActivityTracker))
    suite.addTests(loader.loadTestsFromTestCase(TestActivityViews))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    if result.wasSuccessful():
        print("\n✅ All tests passed!")
    else:
        print(f"\n❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")

        for test, traceback in result.failures:
            print(f"\nFAILURE: {test}")
            print(traceback)

        for test, traceback in result.errors:
            print(f"\nERROR: {test}")
            print(traceback)
