#!/usr/bin/env python3
"""
Test script to verify timestamp accuracy in the application system.
This tests the timestamp parsing and Discord formatting functionality.
"""

import pymongo
from datetime import datetime, timezone
import json

def test_timestamp_parsing():
    """Test the timestamp parsing logic used in the embed"""
    print("🕐 Testing Timestamp Parsing Logic")
    print("=" * 50)
    
    # Test cases for different timestamp formats
    test_cases = [
        {
            "name": "ISO format with Z (UTC)",
            "value": "2025-08-09T14:30:45.123456Z",
            "expected_format": "UTC timezone"
        },
        {
            "name": "ISO format with timezone offset",
            "value": "2025-08-09T14:30:45.123456+00:00",
            "expected_format": "UTC timezone"
        },
        {
            "name": "ISO format without timezone",
            "value": "2025-08-09T14:30:45.123456",
            "expected_format": "Assume UTC"
        },
        {
            "name": "Current timestamp format",
            "value": datetime.now(timezone.utc).isoformat(),
            "expected_format": "Current UTC time"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📝 Test: {test_case['name']}")
        print(f"   Input: {test_case['value']}")
        
        try:
            timestamp_value = test_case['value']
            
            # Apply the same parsing logic as in the embed
            if isinstance(timestamp_value, str):
                if timestamp_value.endswith('Z'):
                    response_time = datetime.fromisoformat(timestamp_value.replace('Z', '+00:00'))
                elif '+' in timestamp_value or timestamp_value.endswith('00:00'):
                    response_time = datetime.fromisoformat(timestamp_value)
                else:
                    response_time = datetime.fromisoformat(timestamp_value).replace(tzinfo=timezone.utc)
            
            # Ensure timezone awareness
            if response_time.tzinfo is None:
                response_time = response_time.replace(tzinfo=timezone.utc)
            
            # Convert to Unix timestamp for Discord
            unix_timestamp = int(response_time.timestamp())
            
            print(f"   Parsed: {response_time}")
            print(f"   Unix timestamp: {unix_timestamp}")
            print(f"   Discord format: <t:{unix_timestamp}:F>")
            print(f"   Discord relative: <t:{unix_timestamp}:R>")
            print(f"   ✅ Success")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")

def check_database_timestamps():
    """Check actual timestamps in the MongoDB database"""
    print("\n\n🗄️ Checking Database Timestamps")
    print("=" * 50)
    
    try:
        client = pymongo.MongoClient("mongodb://localhost:27017/")
        db = client["missminutesbot"]
        collection = db["applications"]
        
        # Get the applications document
        doc = collection.find_one({"_id": "applications"})
        if not doc:
            print("❌ No applications document found")
            return
        
        status_data = doc.get("status", {})
        if not status_data:
            print("❌ No application status data found")
            return
        
        print(f"📊 Found {len(status_data)} applications in database")
        
        # Check each application for timestamp fields
        for user_id, app_data in status_data.items():
            print(f"\n👤 User: {user_id}")
            print(f"   Application: {app_data.get('application_name', 'Unknown')}")
            print(f"   Responded: {app_data.get('responded', False)}")
            
            # Check for various timestamp fields
            timestamp_fields = ["response_time", "processed_time", "admin_response_time", "submission_time"]
            
            for field in timestamp_fields:
                if field in app_data:
                    value = app_data[field]
                    print(f"   {field}: {value} ({type(value).__name__})")
                    
                    # Test parsing this timestamp
                    try:
                        if isinstance(value, str):
                            if value.endswith('Z'):
                                parsed_time = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            elif '+' in value or value.endswith('00:00'):
                                parsed_time = datetime.fromisoformat(value)
                            else:
                                parsed_time = datetime.fromisoformat(value).replace(tzinfo=timezone.utc)
                        
                        if parsed_time.tzinfo is None:
                            parsed_time = parsed_time.replace(tzinfo=timezone.utc)
                        
                        unix_ts = int(parsed_time.timestamp())
                        print(f"     → Parsed: {parsed_time}")
                        print(f"     → Unix: {unix_ts}")
                        print(f"     → Discord: <t:{unix_ts}:F>")
                        
                    except Exception as e:
                        print(f"     → ❌ Parse error: {e}")
            
            if not any(field in app_data for field in timestamp_fields):
                print("   ⚠️ No timestamp fields found")
                
    except Exception as e:
        print(f"❌ Database error: {e}")

def test_discord_timestamp_formats():
    """Test Discord timestamp formatting"""
    print("\n\n📅 Testing Discord Timestamp Formats")
    print("=" * 50)
    
    # Use current time for testing
    now = datetime.now(timezone.utc)
    unix_ts = int(now.timestamp())
    
    print(f"Current time: {now}")
    print(f"Unix timestamp: {unix_ts}")
    print()
    
    formats = [
        ("F", "Full date/time"),
        ("R", "Relative time"),
        ("f", "Short date/time"),
        ("D", "Date only"),
        ("T", "Time only"),
        ("t", "Short time"),
        ("d", "Short date")
    ]
    
    for format_code, description in formats:
        discord_format = f"<t:{unix_ts}:{format_code}>"
        print(f"{description:20} → {discord_format}")

def verify_timestamp_consistency():
    """Verify that our timestamp handling is consistent"""
    print("\n\n🔍 Verifying Timestamp Consistency")
    print("=" * 50)
    
    # Simulate the timestamp creation process
    print("1. Creating timestamp (as done when processing applications):")
    current_time = datetime.now(timezone.utc)
    iso_timestamp = current_time.isoformat()
    print(f"   current_time = datetime.now(timezone.utc)")
    print(f"   iso_timestamp = current_time.isoformat()")
    print(f"   Result: {iso_timestamp}")
    
    print("\n2. Parsing timestamp (as done in the embed):")
    try:
        # Apply embed parsing logic
        if iso_timestamp.endswith('Z'):
            parsed_time = datetime.fromisoformat(iso_timestamp.replace('Z', '+00:00'))
        elif '+' in iso_timestamp or iso_timestamp.endswith('00:00'):
            parsed_time = datetime.fromisoformat(iso_timestamp)
        else:
            parsed_time = datetime.fromisoformat(iso_timestamp).replace(tzinfo=timezone.utc)
        
        if parsed_time.tzinfo is None:
            parsed_time = parsed_time.replace(tzinfo=timezone.utc)
        
        unix_ts = int(parsed_time.timestamp())
        
        print(f"   Parsed: {parsed_time}")
        print(f"   Unix timestamp: {unix_ts}")
        
        # Check if they match
        original_unix = int(current_time.timestamp())
        if original_unix == unix_ts:
            print(f"   ✅ Timestamps match! ({original_unix} == {unix_ts})")
        else:
            print(f"   ❌ Timestamps don't match! ({original_unix} != {unix_ts})")
            
    except Exception as e:
        print(f"   ❌ Parsing error: {e}")

def main():
    """Run all timestamp tests"""
    test_timestamp_parsing()
    check_database_timestamps()
    test_discord_timestamp_formats()
    verify_timestamp_consistency()
    
    print("\n" + "=" * 50)
    print("🎯 Summary:")
    print("- The embed uses 'response_time' field from database")
    print("- Timestamps are stored as ISO format strings")
    print("- Parsing handles multiple timezone formats")
    print("- Discord timestamps use Unix format")
    print("- Full format: <t:timestamp:F>")
    print("- Relative format: <t:timestamp:R>")

if __name__ == "__main__":
    main()
