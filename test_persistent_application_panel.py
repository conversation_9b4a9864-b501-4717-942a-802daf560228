#!/usr/bin/env python3
"""
Test script for the persistent application panel system.
This script tests the timeout fix, health monitoring, and automatic recreation.
"""

import asyncio
import discord
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import json

class MockUser:
    def __init__(self, user_id=12345, name="TestUser", discriminator="0001"):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.display_name = f"{name}#{discriminator}"
        self.mention = f"<@{user_id}>"
        self.guild_permissions = MagicMock()
        self.guild_permissions.administrator = True

class MockGuild:
    def __init__(self, guild_id=67890, name="Test Guild"):
        self.id = guild_id
        self.name = name
        self.icon = MagicMock()
        self.icon.url = "https://example.com/icon.png"

class MockChannel:
    def __init__(self, channel_id, name):
        self.id = channel_id
        self.name = name
        self.mention = f"<#{channel_id}>"
        self.guild = MockGuild()
        self.messages = {}
    
    async def fetch_message(self, message_id):
        if message_id in self.messages:
            return self.messages[message_id]
        raise discord.NotFound(MagicMock(), "Message not found")
    
    async def send(self, content=None, embed=None, view=None):
        message_id = len(self.messages) + 1
        message = MockMessage(message_id, content, embed, view)
        self.messages[message_id] = message
        return message

class MockMessage:
    def __init__(self, message_id, content=None, embed=None, view=None):
        self.id = message_id
        self.content = content
        self.embeds = [embed] if embed else []
        self.view = view
    
    async def edit(self, content=None, embed=None, view=None):
        if content is not None:
            self.content = content
        if embed is not None:
            self.embeds = [embed]
        if view is not None:
            self.view = view
    
    async def delete(self):
        pass

class MockInteraction:
    def __init__(self, user):
        self.user = user
        self.guild = MockGuild()
        self.response = MagicMock()
        self.response.send_message = AsyncMock()
        self.response.defer = AsyncMock()
        self.response.is_done = MagicMock(return_value=False)
        self.followup = MagicMock()
        self.followup.send = AsyncMock()

async def test_persistent_dropdown_creation():
    """Test that PersistentApplicationDropdown is created correctly"""
    print("=== Test 1: Persistent Dropdown Creation ===")
    
    # Mock application forms
    mock_forms = {
        "Police Officer": ["Question 1", "Question 2"],
        "EMS": ["Question 1", "Question 2", "Question 3"],
        "Fire Department": ["Question 1"]
    }
    
    with patch('bot.application_forms', mock_forms):
        from bot import PersistentApplicationDropdown
        
        # Create dropdown
        dropdown = PersistentApplicationDropdown()
        
        print(f"✅ PersistentApplicationDropdown created successfully")
        print(f"   Custom ID: {dropdown.custom_id}")
        print(f"   Placeholder: {dropdown.placeholder}")
        print(f"   Options: {len(dropdown.options)}")
        
        # Check options
        for option in dropdown.options:
            print(f"   - {option.label}: {option.description}")
        
        return True

async def test_persistent_panel_view():
    """Test that PersistentApplicationPanelView works correctly"""
    print("\n=== Test 2: Persistent Panel View ===")
    
    # Mock application forms
    mock_forms = {
        "Police Officer": ["Question 1", "Question 2"],
        "EMS": ["Question 1", "Question 2", "Question 3"]
    }
    
    with patch('bot.application_forms', mock_forms):
        from bot import PersistentApplicationPanelView
        
        # Create view
        view = PersistentApplicationPanelView()
        
        print(f"✅ PersistentApplicationPanelView created successfully")
        print(f"   Timeout: {view.timeout}")
        print(f"   Children: {len(view.children)}")
        print(f"   Created at: {view.created_at}")
        print(f"   Last interaction: {view.last_interaction}")
        
        # Test interaction time update
        old_time = view.last_interaction
        await asyncio.sleep(0.1)
        view.update_interaction_time()
        new_time = view.last_interaction
        
        if new_time > old_time:
            print("✅ Interaction time update working correctly")
        else:
            print("❌ Interaction time update failed")
            return False
        
        return True

async def test_health_monitoring():
    """Test application panel health monitoring"""
    print("\n=== Test 3: Health Monitoring ===")
    
    # Mock channel and message
    channel = MockChannel(5000, "applications")
    message = MockMessage(1001, "Test message")
    channel.messages[1001] = message
    
    # Mock persistent panel data
    mock_panel = {
        "view": MagicMock(),
        "message": message,
        "channel_id": 5000,
        "last_health_check": None,
        "recreation_count": 0,
        "health_check_failures": 0
    }
    
    # Mock view with proper attributes
    mock_view = MagicMock()
    mock_view.children = [MagicMock()]  # Mock dropdown
    mock_view.is_finished.return_value = False
    mock_panel["view"] = mock_view
    
    with patch('bot.persistent_application_panel', mock_panel), \
         patch('bot.bot') as mock_bot:
        
        mock_bot.get_channel.return_value = channel
        
        from bot import check_application_panel_health
        
        # Test healthy panel
        health_ok = await check_application_panel_health()
        
        print(f"✅ Health check result: {health_ok}")
        print(f"   Last health check: {mock_panel['last_health_check']}")
        print(f"   Health check failures: {mock_panel['health_check_failures']}")
        
        if health_ok:
            print("✅ Health monitoring working correctly")
        else:
            print("❌ Health monitoring failed")
            return False
        
        return True

async def test_automatic_recreation():
    """Test automatic panel recreation"""
    print("\n=== Test 4: Automatic Recreation ===")
    
    # Mock channel
    channel = MockChannel(5000, "applications")
    
    # Mock persistent panel with failures
    mock_panel = {
        "view": None,  # Simulate missing view
        "message": None,
        "channel_id": 5000,
        "last_health_check": None,
        "recreation_count": 0,
        "health_check_failures": 5  # Above threshold
    }
    
    # Mock application forms
    mock_forms = {
        "Police Officer": ["Question 1", "Question 2"]
    }
    
    with patch('bot.persistent_application_panel', mock_panel), \
         patch('bot.application_forms', mock_forms), \
         patch('bot.application_channel', 5000), \
         patch('bot.bot') as mock_bot, \
         patch('bot.cleanup_old_application_panel', AsyncMock()), \
         patch('bot.recreate_application_panel', AsyncMock(return_value=True)):
        
        mock_bot.get_channel.return_value = channel
        
        from bot import recreate_application_panel_if_needed
        
        # Test recreation
        result = await recreate_application_panel_if_needed()
        
        print(f"✅ Recreation result: {result}")
        print(f"   Recreation count: {mock_panel['recreation_count']}")
        print(f"   Health check failures: {mock_panel['health_check_failures']}")
        
        if result:
            print("✅ Automatic recreation working correctly")
        else:
            print("❌ Automatic recreation failed")
            return False
        
        return True

async def test_panel_creation():
    """Test persistent panel creation"""
    print("\n=== Test 5: Panel Creation ===")
    
    # Mock channel
    channel = MockChannel(5000, "applications")
    
    # Mock application forms
    mock_forms = {
        "Police Officer": ["Question 1", "Question 2"],
        "EMS": ["Question 1", "Question 2", "Question 3"]
    }
    
    # Mock persistent panel storage
    mock_panel = {
        "view": None,
        "message": None,
        "channel_id": None,
        "last_health_check": None,
        "recreation_count": 0,
        "health_check_failures": 0
    }
    
    with patch('bot.application_forms', mock_forms), \
         patch('bot.persistent_application_panel', mock_panel):
        
        from bot import send_application_embed
        
        # Test panel creation
        message = await send_application_embed(channel)
        
        print(f"✅ Panel creation result: {message is not None}")
        print(f"   Message ID: {message.id if message else 'None'}")
        print(f"   View stored: {mock_panel['view'] is not None}")
        print(f"   Channel ID stored: {mock_panel['channel_id']}")
        
        if message and mock_panel["view"]:
            print("✅ Panel creation working correctly")
        else:
            print("❌ Panel creation failed")
            return False
        
        return True

async def test_command_functionality():
    """Test the check_application_panel command"""
    print("\n=== Test 6: Command Functionality ===")
    
    # Mock channel and forms
    channel = MockChannel(5000, "applications")
    mock_forms = {
        "Police Officer": ["Question 1", "Question 2"]
    }
    
    # Mock persistent panel
    mock_panel = {
        "view": MagicMock(),
        "message": MockMessage(1001, "Test"),
        "channel_id": 5000,
        "last_health_check": datetime.now(),
        "recreation_count": 1,
        "health_check_failures": 0
    }
    
    # Mock view with proper attributes
    mock_view = MagicMock()
    mock_view.created_at = datetime.now() - timedelta(hours=1)
    mock_view.last_interaction = datetime.now() - timedelta(minutes=30)
    mock_panel["view"] = mock_view
    
    with patch('bot.application_forms', mock_forms), \
         patch('bot.application_channel', 5000), \
         patch('bot.persistent_application_panel', mock_panel), \
         patch('bot.bot') as mock_bot, \
         patch('bot.check_application_panel_health', AsyncMock(return_value=True)):
        
        mock_bot.get_channel.return_value = channel
        
        from bot import check_application_panel
        
        # Mock admin user
        admin_user = MockUser(99999, "AdminUser")
        interaction = MockInteraction(admin_user)
        
        try:
            await check_application_panel(interaction, force_recreate=False)
            print("✅ Command executed successfully")
            
            # Verify interaction was called
            interaction.response.defer.assert_called_once()
            interaction.followup.send.assert_called_once()
            print("✅ Command response sent correctly")
            
        except Exception as e:
            print(f"❌ Command execution failed: {e}")
            return False
        
        return True

async def main():
    """Run all tests"""
    print("🧪 Persistent Application Panel System Tests")
    print("=" * 60)
    
    tests = [
        test_persistent_dropdown_creation,
        test_persistent_panel_view,
        test_health_monitoring,
        test_automatic_recreation,
        test_panel_creation,
        test_command_functionality
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            result = await test()
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")
            failed += 1
    
    print(f"\n📊 Test Results")
    print("=" * 40)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed / (passed + failed)) * 100:.1f}%")
    
    print(f"\n💡 Key Features Tested:")
    print("1. ✅ Persistent dropdown with custom_id for timeout resistance")
    print("2. ✅ Persistent view with timeout=None")
    print("3. ✅ Health monitoring system for panel status")
    print("4. ✅ Automatic recreation when health checks fail")
    print("5. ✅ Panel creation with proper storage")
    print("6. ✅ Administrative command for manual checks")
    
    print(f"\n🚀 System Improvements:")
    print("• Persistent views that never timeout")
    print("• Automatic health monitoring every 5 minutes")
    print("• Intelligent recreation with failure thresholds")
    print("• Comprehensive error handling and logging")
    print("• Administrative tools for manual intervention")
    print("• Edge case handling for Discord API issues")

if __name__ == "__main__":
    asyncio.run(main())
