#!/usr/bin/env python3
"""
Test script for the fixed application notification system.
This script tests the dual notification system with proper fallback handling.
"""

import asyncio
import discord
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

class MockUser:
    def __init__(self, user_id=12345, name="TestUser", discriminator="0001", dm_enabled=True):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.mention = f"<@{user_id}>"
        self.dm_enabled = dm_enabled
    
    async def send(self, embed=None, content=None):
        if not self.dm_enabled:
            raise discord.errors.Forbidden(MagicMock(), "Cannot send messages to this user")
        print(f"✅ DM sent to {self.name}: {embed.title if embed else content}")
        return MagicMock()

class MockChannel:
    def __init__(self, channel_id=67890, name="test-channel"):
        self.id = channel_id
        self.name = name
        self.mention = f"<#{channel_id}>"
        self.sent_messages = []
    
    async def send(self, content=None, embed=None):
        message_info = {
            "content": content,
            "embed_title": embed.title if embed else None,
            "embed_color": embed.color.value if embed and embed.color else None
        }
        self.sent_messages.append(message_info)
        
        print(f"✅ Channel message sent to #{self.name}:")
        if content:
            print(f"   Content: {content}")
        if embed:
            print(f"   Embed: {embed.title}")
        return MagicMock()

async def test_dm_notification_with_fallback():
    """Test the improved DM notification system with fallback"""
    print("=== Testing DM Notification with Fallback System ===")
    
    # Test 1: Successful DM
    print("\n🟢 Test 1: Successful DM notification...")
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_fallback_channel', None):
        
        from bot import send_application_notification
        
        user = MockUser(12345, "TestUser", "0001", dm_enabled=True)
        embed = discord.Embed(title="Test Application Approved", color=0x2ECC71)
        
        result = await send_application_notification(user, embed, "Police Officer", "accepted")
        
        if result:
            print("✅ DM notification sent successfully")
        else:
            print("❌ DM notification failed unexpectedly")
    
    # Test 2: DM failure with fallback channel
    print("\n🟡 Test 2: DM failure with fallback channel...")
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_fallback_channel', 99999):
        
        fallback_channel = MockChannel(99999, "application-fallback")
        mock_bot.get_channel.return_value = fallback_channel
        
        from bot import send_application_notification
        
        user = MockUser(12345, "TestUser", "0001", dm_enabled=False)  # DMs disabled
        embed = discord.Embed(title="Test Application Approved", color=0x2ECC71)
        
        result = await send_application_notification(user, embed, "Police Officer", "accepted")
        
        if result:
            print("✅ Fallback notification sent successfully")
            # Check that user mention was sent as separate content
            last_message = fallback_channel.sent_messages[-1]
            if last_message["content"] == user.mention:
                print("✅ User mention correctly placed outside embed in fallback")
            else:
                print(f"❌ User mention issue in fallback: {last_message['content']}")
        else:
            print("❌ Fallback notification failed")
    
    # Test 3: DM failure without fallback channel
    print("\n🔴 Test 3: DM failure without fallback channel...")
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_fallback_channel', None):
        
        from bot import send_application_notification
        
        user = MockUser(12345, "TestUser", "0001", dm_enabled=False)  # DMs disabled
        embed = discord.Embed(title="Test Application Approved", color=0x2ECC71)
        
        result = await send_application_notification(user, embed, "Police Officer", "accepted")
        
        if not result:
            print("✅ Correctly handled missing fallback channel (returned False)")
        else:
            print("❌ Should have failed without fallback channel")

async def test_dual_notification_system():
    """Test that both notification systems work together"""
    print("\n=== Testing Dual Notification System ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890), \
         patch('bot.application_fallback_channel', 99999):
        
        response_channel = MockChannel(67890, "application-responses")
        fallback_channel = MockChannel(99999, "application-fallback")
        mock_bot.get_channel.side_effect = lambda cid: {
            67890: response_channel,
            99999: fallback_channel
        }.get(cid)
        
        from bot import send_application_notification, send_application_response_notification
        
        # Test with user who has DMs disabled
        user = MockUser(12345, "TestUser", "0001", dm_enabled=False)
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        # Create test embed for DM notification
        user_embed = discord.Embed(
            title="Application Approved",
            description="Your application has been approved",
            color=0x2ECC71
        )
        
        print("📤 Sending dual notifications...")
        
        # Send applicant notification (should use fallback)
        dm_result = await send_application_notification(user, user_embed, "Police Officer", "accepted")
        
        # Send staff notification
        staff_result = await send_application_response_notification(
            user, "Police Officer", "accepted", staff_member, feedback="Great application!"
        )
        
        print(f"📊 Results:")
        print(f"   Applicant notification (fallback): {'✅ Success' if dm_result else '❌ Failed'}")
        print(f"   Staff notification: {'✅ Success' if staff_result else '❌ Failed'}")
        
        # Verify both channels received messages
        if len(fallback_channel.sent_messages) > 0:
            print("✅ Fallback channel received applicant notification")
        else:
            print("❌ Fallback channel did not receive notification")
            
        if len(response_channel.sent_messages) > 0:
            print("✅ Response channel received staff notification")
            # Check staff notification format
            last_staff_message = response_channel.sent_messages[-1]
            if last_staff_message["content"] == user.mention:
                print("✅ Staff notification has user mention as separate content")
            if last_staff_message["embed_color"] == 0x1F2937:  # Professional dark green
                print("✅ Staff notification uses professional dark color")
        else:
            print("❌ Response channel did not receive notification")

async def test_configuration_commands():
    """Test the new configuration commands"""
    print("\n=== Testing Configuration Commands ===")
    
    # Mock interaction and channels
    class MockInteraction:
        def __init__(self, user):
            self.user = user
            self.response = MagicMock()
            self.response.send_message = AsyncMock()
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_fallback_channel', None), \
         patch('bot.application_response_channel', None), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        fallback_channel = MockChannel(99999, "application-fallback")
        response_channel = MockChannel(67890, "application-responses")
        mock_bot.get_channel.side_effect = lambda cid: {
            99999: fallback_channel,
            67890: response_channel
        }.get(cid)
        
        from bot import set_application_fallback_channel, check_notification_config
        
        # Test setting fallback channel
        print("📝 Testing fallback channel configuration...")
        admin_user = MockUser(54321, "AdminUser", "0002")
        admin_user.guild_permissions = MagicMock()
        admin_user.guild_permissions.administrator = True
        
        interaction = MockInteraction(admin_user)
        
        try:
            await set_application_fallback_channel(interaction, fallback_channel)
            print("✅ Fallback channel configuration command executed")
        except Exception as e:
            print(f"❌ Fallback channel configuration failed: {e}")
        
        # Test checking configuration
        print("📋 Testing configuration check...")
        try:
            await check_notification_config(interaction)
            print("✅ Configuration check command executed")
        except Exception as e:
            print(f"❌ Configuration check failed: {e}")

async def main():
    """Run all tests"""
    print("🧪 Application Notification System Fix Tests")
    print("=" * 60)
    
    await test_dm_notification_with_fallback()
    await test_dual_notification_system()
    await test_configuration_commands()
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print("✅ All tests completed. Check output above for any failures.")
    print("\n💡 Key Fixes Verified:")
    print("1. Improved DM notification error handling and logging")
    print("2. Proper fallback channel system with user mention outside embed")
    print("3. Dual notification system working independently")
    print("4. Configuration commands for fallback channel setup")
    print("5. Enhanced logging for better debugging")
    print("\n🔧 Next Steps:")
    print("1. Configure fallback channel: /set_application_fallback_channel")
    print("2. Check configuration: /check_notification_config")
    print("3. Test with real application responses")

if __name__ == "__main__":
    asyncio.run(main())
