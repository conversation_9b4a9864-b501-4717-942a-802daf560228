#!/usr/bin/env python3
"""
Timestamp Correction Script for Discord Bot Application System

This script corrects timestamps that were stored with incorrect years due to 
system clock issues. It updates application timestamps from 2025 back to 2024.

IMPORTANT: Only run this script AFTER fixing the system clock!
"""

import pymongo
from datetime import datetime, timezone
import json
from typing import Dict, List, Any

def connect_to_database():
    """Connect to MongoDB database"""
    try:
        client = pymongo.MongoClient("mongodb://localhost:27017/")
        db = client["missminutesbot"]
        return client, db
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None, None

def analyze_timestamp_issues(db) -> Dict[str, Any]:
    """Analyze timestamp issues in the database"""
    print("🔍 ANALYZING TIMESTAMP ISSUES")
    print("=" * 50)
    
    applications_collection = db["applications"]
    app_doc = applications_collection.find_one({"_id": "applications"})
    
    if not app_doc or "status" not in app_doc:
        print("❌ No application data found")
        return {"issues": [], "total_apps": 0}
    
    app_data = app_doc["status"]
    issues = []
    
    current_year = datetime.now().year
    target_year = 2024  # The correct year we want
    
    print(f"📊 Analyzing {len(app_data)} applications")
    print(f"Current system year: {current_year}")
    print(f"Target correction year: {target_year}")
    print()
    
    for user_id, data in app_data.items():
        user_issues = []
        
        # Check timestamp fields
        timestamp_fields = ["response_time", "submission_time", "processed_time"]
        
        for field in timestamp_fields:
            if field in data and data[field]:
                timestamp_str = data[field]
                
                try:
                    # Parse timestamp
                    if timestamp_str.endswith('Z'):
                        parsed_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    elif '+' in timestamp_str:
                        parsed_time = datetime.fromisoformat(timestamp_str)
                    else:
                        parsed_time = datetime.fromisoformat(timestamp_str).replace(tzinfo=timezone.utc)
                    
                    # Check if year needs correction
                    if parsed_time.year == 2025 and target_year == 2024:
                        # Calculate corrected timestamp (subtract 1 year)
                        corrected_time = parsed_time.replace(year=target_year)
                        
                        issue = {
                            'user_id': user_id,
                            'field': field,
                            'original_timestamp': timestamp_str,
                            'original_parsed': parsed_time,
                            'corrected_timestamp': corrected_time.isoformat(),
                            'corrected_parsed': corrected_time
                        }
                        user_issues.append(issue)
                        
                except Exception as e:
                    print(f"⚠️  Failed to parse {field} for user {user_id}: {e}")
        
        if user_issues:
            issues.extend(user_issues)
            print(f"👤 User {user_id}: {len(user_issues)} timestamp(s) need correction")
    
    print(f"\n📋 Summary: {len(issues)} timestamps need correction")
    return {
        "issues": issues,
        "total_apps": len(app_data),
        "affected_users": len(set(issue['user_id'] for issue in issues))
    }

def preview_corrections(issues: List[Dict[str, Any]]):
    """Preview what corrections will be made"""
    print("\n👀 CORRECTION PREVIEW")
    print("=" * 50)
    
    if not issues:
        print("✅ No corrections needed!")
        return
    
    for issue in issues:
        print(f"\n👤 User: {issue['user_id']}")
        print(f"   Field: {issue['field']}")
        print(f"   Original: {issue['original_timestamp']}")
        print(f"   Corrected: {issue['corrected_timestamp']}")
        
        # Show time difference
        time_diff = issue['original_parsed'] - issue['corrected_parsed']
        print(f"   Difference: {time_diff.days} days")

def apply_corrections(db, issues: List[Dict[str, Any]], dry_run: bool = True) -> bool:
    """Apply timestamp corrections to the database"""
    if dry_run:
        print("\n🧪 DRY RUN MODE - No changes will be made")
    else:
        print("\n🔧 APPLYING CORRECTIONS")
    print("=" * 50)
    
    if not issues:
        print("✅ No corrections needed!")
        return True
    
    try:
        applications_collection = db["applications"]
        
        # Group corrections by user
        corrections_by_user = {}
        for issue in issues:
            user_id = issue['user_id']
            if user_id not in corrections_by_user:
                corrections_by_user[user_id] = {}
            corrections_by_user[user_id][issue['field']] = issue['corrected_timestamp']
        
        # Apply corrections
        for user_id, corrections in corrections_by_user.items():
            print(f"👤 Updating user {user_id}: {list(corrections.keys())}")
            
            if not dry_run:
                # Build update query
                update_fields = {}
                for field, corrected_timestamp in corrections.items():
                    update_fields[f"status.{user_id}.{field}"] = corrected_timestamp
                
                # Update database
                result = applications_collection.update_one(
                    {"_id": "applications"},
                    {"$set": update_fields}
                )
                
                if result.modified_count > 0:
                    print(f"   ✅ Updated successfully")
                else:
                    print(f"   ⚠️  No changes made (may already be correct)")
            else:
                print(f"   🧪 Would update: {corrections}")
        
        if not dry_run:
            print(f"\n✅ Applied {len(issues)} corrections to {len(corrections_by_user)} users")
        else:
            print(f"\n🧪 Would apply {len(issues)} corrections to {len(corrections_by_user)} users")
        
        return True
        
    except Exception as e:
        print(f"❌ Error applying corrections: {e}")
        return False

def create_backup(db) -> bool:
    """Create a backup of application data before making changes"""
    print("\n💾 CREATING BACKUP")
    print("=" * 50)
    
    try:
        applications_collection = db["applications"]
        app_doc = applications_collection.find_one({"_id": "applications"})
        
        if app_doc:
            # Create backup filename with timestamp
            backup_filename = f"applications_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # Convert ObjectId to string for JSON serialization
            if '_id' in app_doc:
                app_doc['_id'] = str(app_doc['_id'])
            
            with open(backup_filename, 'w') as f:
                json.dump(app_doc, f, indent=2, default=str)
            
            print(f"✅ Backup created: {backup_filename}")
            return True
        else:
            print("⚠️  No application data to backup")
            return False
            
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return False

def main():
    """Main correction function"""
    print("🔧 TIMESTAMP CORRECTION TOOL")
    print("=" * 60)
    print("This tool corrects timestamps with incorrect years")
    print("IMPORTANT: Fix system clock BEFORE running corrections!")
    print("=" * 60)
    
    # Connect to database
    client, db = connect_to_database()
    if not db:
        return
    
    try:
        # Analyze issues
        analysis = analyze_timestamp_issues(db)
        issues = analysis["issues"]
        
        if not issues:
            print("\n✅ No timestamp corrections needed!")
            return
        
        # Preview corrections
        preview_corrections(issues)
        
        # Ask for confirmation
        print(f"\n❓ Found {len(issues)} timestamps that need correction.")
        print("This will change timestamps from 2025 back to 2024.")
        print("\nOptions:")
        print("1. Dry run (preview only)")
        print("2. Create backup and apply corrections")
        print("3. Cancel")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            # Dry run
            apply_corrections(db, issues, dry_run=True)
            
        elif choice == "2":
            # Create backup first
            if create_backup(db):
                print("\n⚠️  FINAL CONFIRMATION")
                confirm = input("Apply corrections to database? (yes/no): ").strip().lower()
                
                if confirm == "yes":
                    success = apply_corrections(db, issues, dry_run=False)
                    if success:
                        print("\n🎉 Timestamp corrections completed successfully!")
                        print("💡 Remember to restart the Discord bot")
                    else:
                        print("\n❌ Corrections failed. Check the backup file.")
                else:
                    print("❌ Corrections cancelled")
            else:
                print("❌ Cannot proceed without backup")
                
        else:
            print("❌ Operation cancelled")
    
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    main()
