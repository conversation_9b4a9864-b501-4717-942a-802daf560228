"""
Test script for the Discord Activity Dashboard System

This script provides comprehensive testing for the new role-based dashboard
functionality including persistence, pagination, and data handling.
"""

import asyncio
import unittest
from unittest.mock import Mock, AsyncMock, patch
import discord
from datetime import datetime, timezone, timedelta
import sys
import os

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from activity_tracker import (
    ActivityTracker, ActivityPeriod, PersistentRoleDashboardView, 
    RoleDashboardView, MemberActivityData
)

class TestRoleDashboard(unittest.TestCase):
    """Test cases for the role-based dashboard system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tracker = ActivityTracker()
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 12345
        self.mock_guild.name = "Test Guild"
        
        # Create mock roles
        self.mock_role1 = Mock(spec=discord.Role)
        self.mock_role1.id = 111
        self.mock_role1.name = "Staff"
        
        self.mock_role2 = Mock(spec=discord.Role)
        self.mock_role2.id = 222
        self.mock_role2.name = "VIP"
        
        # Create mock members
        self.mock_members = []
        for i in range(15):  # Create 15 members for pagination testing
            member = Mock(spec=discord.Member)
            member.id = 1000 + i
            member.name = f"user{i}"
            member.display_name = f"User {i}"
            member.bot = False
            member.roles = [self.mock_role1]
            self.mock_members.append(member)
        
        self.mock_role1.members = self.mock_members
        self.mock_role2.members = self.mock_members[:5]  # Fewer members for role2
        
        self.mock_guild.get_role = Mock(side_effect=lambda role_id: 
            self.mock_role1 if role_id == 111 else 
            self.mock_role2 if role_id == 222 else None)
    
    def test_persistent_dashboard_creation(self):
        """Test creation of persistent dashboard view"""
        dashboard = PersistentRoleDashboardView(self.tracker, self.mock_guild.id)
        
        self.assertEqual(dashboard.tracker, self.tracker)
        self.assertEqual(dashboard.guild_id, self.mock_guild.id)
        self.assertEqual(dashboard.current_period, ActivityPeriod.WEEKLY)
        self.assertIsNone(dashboard.selected_role_id)
        self.assertEqual(dashboard.current_page, 0)
        self.assertEqual(dashboard.members_per_page, 10)
        
        # Check that components are added
        self.assertGreater(len(dashboard.children), 0)
        
        # Check for specific component types
        has_select = any(isinstance(item, discord.ui.Select) for item in dashboard.children)
        has_buttons = any(isinstance(item, discord.ui.Button) for item in dashboard.children)
        
        self.assertTrue(has_select, "Dashboard should have a role selection dropdown")
        self.assertTrue(has_buttons, "Dashboard should have navigation buttons")
    
    def test_regular_dashboard_creation(self):
        """Test creation of regular (non-persistent) dashboard view"""
        dashboard = RoleDashboardView(self.tracker, self.mock_guild)
        
        self.assertEqual(dashboard.tracker, self.tracker)
        self.assertEqual(dashboard.guild, self.mock_guild)
        self.assertEqual(dashboard.current_period, ActivityPeriod.WEEKLY)
        self.assertEqual(dashboard.timeout, 600)  # 10 minutes
    
    @patch('activity_tracker.bot')
    async def test_role_dropdown_update(self, mock_bot):
        """Test updating role dropdown with tracked roles"""
        mock_bot.get_guild.return_value = self.mock_guild
        
        # Set up tracked roles
        self.tracker.tracked_roles = {111, 222}
        
        dashboard = PersistentRoleDashboardView(self.tracker, self.mock_guild.id)
        await dashboard._update_role_dropdown()
        
        # Find the select component
        select_component = None
        for item in dashboard.children:
            if isinstance(item, discord.ui.Select):
                select_component = item
                break
        
        self.assertIsNotNone(select_component, "Should have a select component")
        self.assertEqual(len(select_component.options), 2, "Should have 2 role options")
        
        # Check option values
        option_values = [opt.value for opt in select_component.options]
        self.assertIn("111", option_values)
        self.assertIn("222", option_values)
    
    async def test_role_data_loading(self):
        """Test loading activity data for role members"""
        # Mock the get_member_activity method
        async def mock_get_activity(member, period):
            return MemberActivityData(
                user_id=member.id,
                username=member.name,
                display_name=member.display_name,
                tracked_roles=[111],
                total_messages=100 + member.id,  # Varying activity
                total_voice_time=3600 + (member.id * 60),
                total_active_hours=5.0,
                voice_sessions=[],
                message_activity=[],
                last_seen=datetime.now(timezone.utc),
                tracking_start=datetime.now(timezone.utc).date()
            )
        
        self.tracker.get_member_activity = mock_get_activity
        
        with patch('activity_tracker.bot') as mock_bot:
            mock_bot.get_guild.return_value = self.mock_guild
            
            dashboard = PersistentRoleDashboardView(self.tracker, self.mock_guild.id)
            dashboard.selected_role_id = 111
            
            await dashboard._load_role_data()
            
            # Check that data was loaded
            self.assertEqual(len(dashboard.role_members_data), 15)
            self.assertIsNotNone(dashboard.role_summary)
            self.assertEqual(dashboard.role_summary["role_name"], "Staff")
            self.assertEqual(dashboard.role_summary["member_count"], 15)
            
            # Check that data is sorted by activity
            activities = [data.total_messages + (data.total_voice_time // 60) 
                         for data in dashboard.role_members_data]
            self.assertEqual(activities, sorted(activities, reverse=True))
    
    async def test_pagination_logic(self):
        """Test pagination functionality"""
        dashboard = PersistentRoleDashboardView(self.tracker, self.mock_guild.id)
        dashboard.members_per_page = 10
        
        # Create mock data for 25 members
        dashboard.role_members_data = []
        for i in range(25):
            data = MemberActivityData(
                user_id=i,
                username=f"user{i}",
                display_name=f"User {i}",
                tracked_roles=[111],
                total_messages=100,
                total_voice_time=3600,
                total_active_hours=5.0,
                voice_sessions=[],
                message_activity=[],
                last_seen=datetime.now(timezone.utc),
                tracking_start=datetime.now(timezone.utc).date()
            )
            dashboard.role_members_data.append(data)
        
        # Test pagination calculations
        total_pages = max(1, (len(dashboard.role_members_data) + dashboard.members_per_page - 1) // dashboard.members_per_page)
        self.assertEqual(total_pages, 3)  # 25 members / 10 per page = 3 pages
        
        # Test page navigation
        dashboard.current_page = 0
        dashboard._update_pagination_buttons()
        
        # Check button states for first page
        prev_button = next((item for item in dashboard.children 
                          if isinstance(item, discord.ui.Button) and 
                          item.custom_id and item.custom_id.endswith("_prev_page")), None)
        next_button = next((item for item in dashboard.children 
                          if isinstance(item, discord.ui.Button) and 
                          item.custom_id and item.custom_id.endswith("_next_page")), None)
        
        if prev_button:
            self.assertTrue(prev_button.disabled, "Previous button should be disabled on first page")
        if next_button:
            self.assertFalse(next_button.disabled, "Next button should be enabled when more pages exist")
    
    async def test_period_switching(self):
        """Test switching between time periods"""
        dashboard = PersistentRoleDashboardView(self.tracker, self.mock_guild.id)
        
        # Test initial period
        self.assertEqual(dashboard.current_period, ActivityPeriod.WEEKLY)
        
        # Mock interaction for period change
        mock_interaction = Mock(spec=discord.Interaction)
        mock_interaction.response.defer = AsyncMock()
        mock_interaction.edit_original_response = AsyncMock()
        
        # Test period change
        await dashboard._update_period(mock_interaction, ActivityPeriod.MONTHLY)
        
        self.assertEqual(dashboard.current_period, ActivityPeriod.MONTHLY)
        self.assertEqual(dashboard.current_page, 0)  # Should reset to first page
    
    @patch('activity_tracker.bot')
    async def test_embed_creation(self, mock_bot):
        """Test dashboard embed creation"""
        mock_bot.get_guild.return_value = self.mock_guild
        
        dashboard = PersistentRoleDashboardView(self.tracker, self.mock_guild.id)
        
        # Test default embed (no role selected)
        embed = await dashboard._create_dashboard_embed()
        self.assertEqual(embed.title, "📊 Activity Dashboard")
        self.assertIn("Select a tracked role", embed.description)
        
        # Test role-specific embed
        dashboard.selected_role_id = 111
        dashboard.role_summary = {
            "role_name": "Staff",
            "member_count": 15,
            "total_messages": 1500,
            "total_voice_time": 54000,  # 15 hours
            "total_active_hours": 75.0,
            "period": "weekly"
        }
        dashboard.role_members_data = []  # Empty for this test
        
        embed = await dashboard._create_dashboard_embed()
        self.assertEqual(embed.title, "📊 Staff Activity Dashboard")
        self.assertIn("15 members", embed.description)
        
        # Check for summary field
        summary_field = next((field for field in embed.fields 
                            if "Role Summary" in field.name), None)
        self.assertIsNotNone(summary_field, "Should have a role summary field")
        self.assertIn("1,500", summary_field.value)  # Total messages

class TestDashboardIntegration(unittest.TestCase):
    """Integration tests for the complete dashboard system"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.tracker = ActivityTracker()
        self.tracker.tracked_roles = {111, 222}
        self.tracker.authorized_roles = {333}
    
    async def test_dashboard_workflow(self):
        """Test the complete dashboard workflow"""
        # This would test the full workflow from command to display
        # In a real environment with proper mocking
        pass

def run_async_test(coro):
    """Helper function to run async tests"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

if __name__ == "__main__":
    # Run the tests
    print("Running Activity Dashboard Tests...")
    
    # Create test loader
    loader = unittest.TestLoader()
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestRoleDashboard))
    suite.addTests(loader.loadTestsFromTestCase(TestDashboardIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    if result.wasSuccessful():
        print("\n✅ All dashboard tests passed!")
    else:
        print(f"\n❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        for test, traceback in result.failures:
            print(f"\nFAILURE: {test}")
            print(traceback)
            
        for test, traceback in result.errors:
            print(f"\nERROR: {test}")
            print(traceback)
