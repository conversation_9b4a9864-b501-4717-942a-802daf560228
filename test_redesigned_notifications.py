#!/usr/bin/env python3
"""
Test script for the redesigned application response notification system.
This script tests the new design specifications including:
- User mention placement outside embed
- Professional dark colors
- Compact horizontal layout
- "Reason" terminology instead of "Feedback"
"""

import asyncio
import discord
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

class MockUser:
    def __init__(self, user_id=12345, name="TestUser", discriminator="0001"):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.mention = f"<@{user_id}>"

class MockChannel:
    def __init__(self, channel_id=67890, name="application-responses"):
        self.id = channel_id
        self.name = name
        self.mention = f"<#{channel_id}>"
        self.sent_messages = []
    
    async def send(self, content=None, embed=None):
        message_info = {
            "content": content,
            "embed": {
                "title": embed.title if embed else None,
                "description": embed.description if embed else None,
                "color": embed.color.value if embed and embed.color else None,
                "fields": [{"name": f.name, "value": f.value, "inline": f.inline} for f in embed.fields] if embed else [],
                "footer": embed.footer.text if embed and embed.footer else None
            } if embed else None
        }
        self.sent_messages.append(message_info)
        
        print(f"📤 Message sent to #{self.name}:")
        if content:
            print(f"   Content: {content}")
        if embed:
            print(f"   Embed Title: {embed.title}")
            print(f"   Embed Description: {embed.description}")
            print(f"   Embed Color: #{embed.color.value:06x}" if embed.color else "   Embed Color: None")
            print(f"   Fields: {len(embed.fields)}")
            for field in embed.fields:
                print(f"     - {field.name}: {field.value[:50]}{'...' if len(field.value) > 50 else ''} (inline: {field.inline})")
            if embed.footer:
                print(f"   Footer: {embed.footer.text}")
        print()
        return MagicMock()

async def test_redesigned_notification_function():
    """Test the redesigned send_application_response_notification function"""
    print("=== Testing Redesigned Application Response Notification ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890):
        
        response_channel = MockChannel(67890, "application-responses")
        mock_bot.get_channel.return_value = response_channel
        
        # Import after patching
        from bot import send_application_response_notification
        
        # Test approval notification
        print("🟢 Testing APPROVAL notification...")
        user = MockUser(12345, "TestApplicant", "0001")
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        try:
            result = await send_application_response_notification(
                user, "Police Officer", "accepted", staff_member, 
                feedback="Excellent application with strong background"
            )
            
            if result:
                print("✅ Approval notification sent successfully")
                
                # Verify the message structure
                last_message = response_channel.sent_messages[-1]
                
                # Check user mention is in content (outside embed)
                if last_message["content"] == user.mention:
                    print("✅ User mention correctly placed outside embed")
                else:
                    print(f"❌ User mention issue: {last_message['content']}")
                
                # Check embed color (should be dark green #1F2937)
                expected_color = 0x1F2937
                if last_message["embed"]["color"] == expected_color:
                    print("✅ Professional dark green color applied")
                else:
                    print(f"❌ Color issue: got #{last_message['embed']['color']:06x}, expected #{expected_color:06x}")
                
                # Check embed title focuses on decision
                if "Approved" in last_message["embed"]["title"]:
                    print("✅ Embed title focuses on approval decision")
                else:
                    print(f"❌ Title issue: {last_message['embed']['title']}")
                
                # Check for compact layout (inline fields)
                inline_fields = [f for f in last_message["embed"]["fields"] if f["inline"]]
                if len(inline_fields) >= 2:
                    print("✅ Compact horizontal layout with inline fields")
                else:
                    print("❌ Layout issue: not enough inline fields for compact design")
                
            else:
                print("❌ Approval notification failed")
                
        except Exception as e:
            print(f"❌ Approval test failed: {e}")
        
        print()
        
        # Test denial notification
        print("🔴 Testing DENIAL notification...")
        try:
            result = await send_application_response_notification(
                user, "Police Officer", "rejected", staff_member, 
                feedback="Application needs more experience in law enforcement"
            )
            
            if result:
                print("✅ Denial notification sent successfully")
                
                # Verify the message structure
                last_message = response_channel.sent_messages[-1]
                
                # Check embed color (should be dark red/burgundy #374151)
                expected_color = 0x374151
                if last_message["embed"]["color"] == expected_color:
                    print("✅ Professional dark red/burgundy color applied")
                else:
                    print(f"❌ Color issue: got #{last_message['embed']['color']:06x}, expected #{expected_color:06x}")
                
                # Check embed title focuses on decision
                if "Denied" in last_message["embed"]["title"]:
                    print("✅ Embed title focuses on denial decision")
                else:
                    print(f"❌ Title issue: {last_message['embed']['title']}")
                
                # Check for "Reason" terminology instead of "Feedback"
                reason_field = next((f for f in last_message["embed"]["fields"] if "Reason" in f["name"]), None)
                if reason_field:
                    print("✅ 'Reason' terminology used instead of 'Feedback'")
                else:
                    print("❌ Terminology issue: 'Reason' field not found")
                
            else:
                print("❌ Denial notification failed")
                
        except Exception as e:
            print(f"❌ Denial test failed: {e}")

async def test_modal_terminology():
    """Test that modal handlers use 'Reason' terminology"""
    print("\n=== Testing Modal Terminology Changes ===")
    
    # Import modal classes
    from bot import AcceptReasonModal, RejectReasonModal
    
    # Test AcceptReasonModal
    print("📝 Testing AcceptReasonModal...")
    user = MockUser(12345, "TestApplicant", "0001")
    accept_modal = AcceptReasonModal(user, "Police Officer")
    
    # Check if the text input label uses "Reason"
    reason_input = next((child for child in accept_modal.children if "Reason" in child.label), None)
    if reason_input:
        print("✅ AcceptReasonModal uses 'Reason for Applicant' label")
    else:
        print("❌ AcceptReasonModal still uses old terminology")
        for child in accept_modal.children:
            print(f"   Found label: {child.label}")
    
    # Test RejectReasonModal
    print("📝 Testing RejectReasonModal...")
    reject_modal = RejectReasonModal(user, "Police Officer")
    
    # Check if the text input label uses "Reason"
    reason_input = next((child for child in reject_modal.children if "Reason" in child.label), None)
    if reason_input:
        print("✅ RejectReasonModal uses 'Reason for Applicant' label")
    else:
        print("❌ RejectReasonModal still uses old terminology")
        for child in reject_modal.children:
            print(f"   Found label: {child.label}")

async def test_design_specifications():
    """Test specific design requirements"""
    print("\n=== Testing Design Specifications ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890):
        
        response_channel = MockChannel(67890, "application-responses")
        mock_bot.get_channel.return_value = response_channel
        
        from bot import send_application_response_notification
        
        user = MockUser(12345, "TestApplicant", "0001")
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        # Test with both feedback and reason parameters
        await send_application_response_notification(
            user, "Police Officer", "accepted", staff_member, 
            feedback="Great application", reason="Strong background"
        )
        
        last_message = response_channel.sent_messages[-1]
        
        # Check that user mention is NOT in embed fields
        embed_text = str(last_message["embed"])
        if user.mention not in embed_text:
            print("✅ User mention removed from embed fields")
        else:
            print("❌ User mention still found in embed")
        
        # Check for minimal footer
        footer_text = last_message["embed"]["footer"]
        if footer_text and len(footer_text) < 50:  # Should be minimal
            print("✅ Professional minimal footer")
        else:
            print(f"❌ Footer too verbose: {footer_text}")
        
        # Check field count (should be compact)
        field_count = len(last_message["embed"]["fields"])
        if field_count <= 3:
            print("✅ Compact design with minimal fields")
        else:
            print(f"❌ Too many fields for compact design: {field_count}")

async def main():
    """Run all tests"""
    print("🧪 Redesigned Application Response Notification Tests")
    print("=" * 60)
    
    await test_redesigned_notification_function()
    await test_modal_terminology()
    await test_design_specifications()
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print("✅ All tests completed. Check output above for any failures.")
    print("\n💡 Key Changes Verified:")
    print("1. User mention placed outside embed as separate content")
    print("2. Professional dark colors (#1F2937 for approved, #374151 for denied)")
    print("3. Compact horizontal layout with inline fields")
    print("4. 'Reason' terminology instead of 'Feedback'")
    print("5. Decision-focused embed titles")
    print("6. Minimal professional footer")

if __name__ == "__main__":
    asyncio.run(main())
