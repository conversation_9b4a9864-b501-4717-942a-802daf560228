#!/usr/bin/env python3
"""
Test script for the unified application notification system.
This script tests the consolidated notification system where application_response_channel
serves both as staff notification channel and DM fallback channel.
"""

import asyncio
import discord
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

class MockUser:
    def __init__(self, user_id=12345, name="TestUser", discriminator="0001", dm_enabled=True):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.mention = f"<@{user_id}>"
        self.dm_enabled = dm_enabled
    
    async def send(self, embed=None, content=None):
        if not self.dm_enabled:
            raise discord.errors.Forbidden(MagicMock(), "Cannot send messages to this user")
        print(f"✅ DM sent to {self.name}: {embed.title if embed else content}")
        return MagicMock()

class MockChannel:
    def __init__(self, channel_id=67890, name="unified-channel"):
        self.id = channel_id
        self.name = name
        self.mention = f"<#{channel_id}>"
        self.sent_messages = []
    
    async def send(self, content=None, embed=None):
        message_info = {
            "content": content,
            "embed_title": embed.title if embed else None,
            "embed_description": embed.description if embed else None,
            "embed_color": embed.color.value if embed and embed.color else None,
            "is_fallback": "DM Failed" in (embed.title if embed else ""),
            "is_staff_notification": embed and hasattr(embed, 'fields') and any("Processed By" in f.name for f in embed.fields)
        }
        self.sent_messages.append(message_info)
        
        print(f"📤 Message sent to #{self.name}:")
        if content:
            print(f"   Content: {content}")
        if embed:
            print(f"   Embed: {embed.title}")
            if "DM Failed" in embed.title:
                print(f"   Type: Fallback Notification")
            elif hasattr(embed, 'fields') and any("Processed By" in f.name for f in embed.fields):
                print(f"   Type: Staff Notification")
        return MagicMock()

async def test_unified_system_always_notify_on():
    """Test unified system with always_notify=True (both DM and channel notifications)"""
    print("=== Test 1: Always Notify ON (DM Success + Channel Notification) ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890), \
         patch('bot.application_response_always_notify', True):
        
        unified_channel = MockChannel(67890, "unified-notifications")
        mock_bot.get_channel.return_value = unified_channel
        
        from bot import send_application_notification
        
        # Test with user who has DMs enabled
        user = MockUser(12345, "TestUser", "0001", dm_enabled=True)
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        embed = discord.Embed(title="Application Approved", color=0x2ECC71)
        
        dm_success, channel_sent = await send_application_notification(
            user, embed, "Police Officer", "accepted", 
            staff_member=staff_member, feedback="Great application!"
        )
        
        print(f"Results: DM Success={dm_success}, Channel Sent={channel_sent}")
        
        if dm_success:
            print("✅ DM sent successfully")
        else:
            print("❌ DM failed unexpectedly")
            
        if channel_sent:
            print("✅ Channel notification sent (always notify enabled)")
            # Check that it's a staff notification, not fallback
            last_message = unified_channel.sent_messages[-1]
            if last_message["is_staff_notification"]:
                print("✅ Correct: Staff notification sent to channel")
            else:
                print("❌ Incorrect: Should be staff notification")
        else:
            print("❌ Channel notification not sent")

async def test_unified_system_always_notify_off_dm_success():
    """Test unified system with always_notify=False and DM success (no channel notification)"""
    print("\n=== Test 2: Always Notify OFF + DM Success (No Channel Notification) ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890), \
         patch('bot.application_response_always_notify', False):
        
        unified_channel = MockChannel(67890, "unified-notifications")
        mock_bot.get_channel.return_value = unified_channel
        
        from bot import send_application_notification
        
        # Test with user who has DMs enabled
        user = MockUser(12345, "TestUser", "0001", dm_enabled=True)
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        embed = discord.Embed(title="Application Approved", color=0x2ECC71)
        
        dm_success, channel_sent = await send_application_notification(
            user, embed, "Police Officer", "accepted", 
            staff_member=staff_member, feedback="Great application!"
        )
        
        print(f"Results: DM Success={dm_success}, Channel Sent={channel_sent}")
        
        if dm_success:
            print("✅ DM sent successfully")
        else:
            print("❌ DM failed unexpectedly")
            
        if not channel_sent:
            print("✅ Channel notification correctly NOT sent (always notify disabled, DM succeeded)")
        else:
            print("❌ Channel notification sent when it shouldn't be")

async def test_unified_system_always_notify_off_dm_fail():
    """Test unified system with always_notify=False and DM failure (fallback notification)"""
    print("\n=== Test 3: Always Notify OFF + DM Failure (Fallback Notification) ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890), \
         patch('bot.application_response_always_notify', False):
        
        unified_channel = MockChannel(67890, "unified-notifications")
        mock_bot.get_channel.return_value = unified_channel
        
        from bot import send_application_notification
        
        # Test with user who has DMs disabled
        user = MockUser(12345, "TestUser", "0001", dm_enabled=False)
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        embed = discord.Embed(title="Application Approved", color=0x2ECC71)
        
        dm_success, channel_sent = await send_application_notification(
            user, embed, "Police Officer", "accepted", 
            staff_member=staff_member, feedback="Great application!"
        )
        
        print(f"Results: DM Success={dm_success}, Channel Sent={channel_sent}")
        
        if not dm_success:
            print("✅ DM correctly failed (user has DMs disabled)")
        else:
            print("❌ DM should have failed")
            
        if channel_sent:
            print("✅ Fallback notification sent to channel")
            # Check that it's a fallback notification, not staff notification
            last_message = unified_channel.sent_messages[-1]
            if last_message["is_fallback"]:
                print("✅ Correct: Fallback notification sent to channel")
            else:
                print("❌ Incorrect: Should be fallback notification")
        else:
            print("❌ Fallback notification not sent")

async def test_unified_system_no_channel_configured():
    """Test unified system with no channel configured"""
    print("\n=== Test 4: No Channel Configured ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', None), \
         patch('bot.application_response_always_notify', True):
        
        from bot import send_application_notification
        
        # Test with user who has DMs disabled
        user = MockUser(12345, "TestUser", "0001", dm_enabled=False)
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        embed = discord.Embed(title="Application Approved", color=0x2ECC71)
        
        dm_success, channel_sent = await send_application_notification(
            user, embed, "Police Officer", "accepted", 
            staff_member=staff_member, feedback="Great application!"
        )
        
        print(f"Results: DM Success={dm_success}, Channel Sent={channel_sent}")
        
        if not dm_success:
            print("✅ DM correctly failed (user has DMs disabled)")
        else:
            print("❌ DM should have failed")
            
        if not channel_sent:
            print("✅ Channel notification correctly not sent (no channel configured)")
        else:
            print("❌ Channel notification sent when no channel configured")

async def test_command_configuration():
    """Test the updated command configuration"""
    print("\n=== Test 5: Command Configuration ===")
    
    # Mock interaction and channels
    class MockInteraction:
        def __init__(self, user):
            self.user = user
            self.response = MagicMock()
            self.response.send_message = AsyncMock()
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_log_channel', None), \
         patch('bot.application_response_channel', None), \
         patch('bot.application_response_always_notify', True), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        log_channel = MockChannel(12345, "application-logs")
        response_channel = MockChannel(67890, "unified-notifications")
        mock_bot.get_channel.side_effect = lambda cid: {
            12345: log_channel,
            67890: response_channel
        }.get(cid)
        
        from bot import set_application_log_channel
        
        # Test setting channels with always_notify=True
        print("📝 Testing channel configuration with always_notify=True...")
        admin_user = MockUser(54321, "AdminUser", "0002")
        admin_user.guild_permissions = MagicMock()
        admin_user.guild_permissions.administrator = True
        
        interaction = MockInteraction(admin_user)
        
        try:
            await set_application_log_channel(interaction, log_channel, response_channel, always_notify=True)
            print("✅ Channel configuration with always_notify=True executed")
        except Exception as e:
            print(f"❌ Channel configuration failed: {e}")
        
        # Test setting channels with always_notify=False
        print("📝 Testing channel configuration with always_notify=False...")
        try:
            await set_application_log_channel(interaction, log_channel, response_channel, always_notify=False)
            print("✅ Channel configuration with always_notify=False executed")
        except Exception as e:
            print(f"❌ Channel configuration failed: {e}")

async def main():
    """Run all tests"""
    print("🧪 Unified Application Notification System Tests")
    print("=" * 60)
    
    await test_unified_system_always_notify_on()
    await test_unified_system_always_notify_off_dm_success()
    await test_unified_system_always_notify_off_dm_fail()
    await test_unified_system_no_channel_configured()
    await test_command_configuration()
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print("✅ All tests completed. Check output above for any failures.")
    print("\n💡 Key Features Verified:")
    print("1. Unified channel serves both staff notifications and DM fallback")
    print("2. Toggle controls when channel notifications are sent")
    print("3. Always notify ON: Channel notifications regardless of DM success")
    print("4. Always notify OFF: Channel notifications only when DM fails")
    print("5. Proper fallback vs staff notification differentiation")
    print("\n🔧 Usage:")
    print("• /set_application_log_channel log_channel:#logs response_channel:#unified always_notify:True")
    print("• /set_application_log_channel log_channel:#logs response_channel:#unified always_notify:False")

if __name__ == "__main__":
    asyncio.run(main())
