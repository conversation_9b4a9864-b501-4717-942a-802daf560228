"""
Activity Tracker Usage Example

This file demonstrates how to use the Discord Activity Tracker System
with example configurations and usage patterns.
"""

import asyncio
import discord
from datetime import datetime, timezone
from activity_tracker import activity_tracker, ActivityPeriod

# Example configuration setup
async def setup_activity_tracker_example():
    """
    Example of how to set up the activity tracker with sample configuration
    """
    print("🔧 Setting up Activity Tracker Example...")
    
    # Initialize the tracker (this would normally be done in bot startup)
    try:
        await activity_tracker.initialize()
        print("✅ Activity tracker initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize activity tracker: {e}")
        return False
    
    # Example role configuration
    # In a real bot, these would be actual Discord role IDs
    example_tracked_roles = {
        123456789,  # Example: "Staff" role
        987654321,  # Example: "VIP" role
        555666777   # Example: "Active Member" role
    }
    
    example_access_roles = {
        111222333,  # Example: "Moderator" role
        444555666   # Example: "Admin" role
    }
    
    # Configure the tracker
    activity_tracker.tracked_roles = example_tracked_roles
    activity_tracker.authorized_roles = example_access_roles
    
    # Save configuration
    await activity_tracker._save_config()
    print("✅ Example configuration saved")
    
    return True

# Example usage patterns
async def demonstrate_activity_tracking():
    """
    Demonstrate various activity tracking features
    """
    print("\n📊 Demonstrating Activity Tracking Features...")
    
    # Example 1: Message tracking simulation
    print("\n1. Message Tracking Example:")
    print("   - When a member sends a message, it's automatically tracked")
    print("   - Messages are batched and saved to database every 5 minutes")
    print("   - Per-channel message counts are maintained")
    
    # Example 2: Voice activity tracking
    print("\n2. Voice Activity Tracking Example:")
    print("   - Voice channel joins/leaves are tracked in real-time")
    print("   - Duration calculations are precise to the second")
    print("   - Per-channel voice time is recorded")
    
    # Example 3: Presence monitoring
    print("\n3. Presence Monitoring Example:")
    print("   - Online/idle/dnd status changes update active hours")
    print("   - Helps calculate total active time on server")
    print("   - Presence cache prevents spam from frequent updates")

async def demonstrate_data_retrieval():
    """
    Demonstrate how to retrieve and display activity data
    """
    print("\n📈 Data Retrieval Examples...")
    
    # Example data structure that would be returned
    example_activity_data = {
        "user_id": 123456789,
        "username": "ExampleUser",
        "display_name": "Example User",
        "total_messages": 150,
        "total_voice_time": 7200,  # 2 hours in seconds
        "voice_sessions": [
            {
                "channel_name": "General Voice",
                "duration_seconds": 3600,
                "start_time": "2024-01-15T10:00:00Z"
            },
            {
                "channel_name": "Gaming Voice",
                "duration_seconds": 3600,
                "start_time": "2024-01-15T14:00:00Z"
            }
        ],
        "message_activity": [
            {
                "channel_name": "general",
                "message_count": 75,
                "last_message_time": "2024-01-15T16:30:00Z"
            },
            {
                "channel_name": "off-topic",
                "message_count": 75,
                "last_message_time": "2024-01-15T15:45:00Z"
            }
        ]
    }
    
    print("Example Activity Data Structure:")
    print(f"  User: {example_activity_data['display_name']}")
    print(f"  Total Messages: {example_activity_data['total_messages']:,}")
    print(f"  Total Voice Time: {example_activity_data['total_voice_time'] // 3600}h {(example_activity_data['total_voice_time'] % 3600) // 60}m")
    print(f"  Voice Sessions: {len(example_activity_data['voice_sessions'])}")
    print(f"  Active Channels: {len(example_activity_data['message_activity'])}")

def demonstrate_discord_commands():
    """
    Show example Discord command usage
    """
    print("\n💬 Discord Command Examples...")

    print("1. Setup Command (Administrator only):")
    print("   /activity_setup")
    print("   - Opens interactive configuration panel")
    print("   - Configure tracked roles and access roles")
    print("   - View current configuration")

    print("\n2. Dashboard Command (Access roles only):")
    print("   /activity_search")
    print("   - Opens comprehensive role-based activity dashboard")
    print("   - Select roles from dropdown menu")
    print("   - View all members in selected role with pagination")
    print("   - Interactive period switching (daily/weekly/monthly)")
    print("   - Real-time data refresh capabilities")

    print("\n3. Dashboard Interface Features:")
    print("   📊 Role Activity Dashboard")
    print("   ├── 🔽 Role Selection Dropdown")
    print("   │   ├── Staff (15 members)")
    print("   │   ├── VIP (8 members)")
    print("   │   └── Moderators (5 members)")
    print("   ├── 📈 Role Summary Statistics")
    print("   │   ├── Total Messages: 2,450")
    print("   │   ├── Total Voice Time: 45h 30m")
    print("   │   └── Average per Member: 163 messages")
    print("   ├── 👥 Member Activity List (Paginated)")
    print("   │   ├── 1. User A: 💬 245 msgs | 🎤 3h 15m | 📊 440 pts")
    print("   │   ├── 2. User B: 💬 198 msgs | 🎤 2h 45m | 📊 363 pts")
    print("   │   └── ... (10 members per page)")
    print("   └── 🎛️ Interactive Controls")
    print("       ├── 📅 Daily | 📊 Weekly | 📈 Monthly")
    print("       ├── ◀️ Previous | Page 1/3 | Next ▶️")
    print("       └── 🔄 Refresh Data")

    print("\n4. Persistence Features:")
    print("   ✅ Dashboard survives bot restarts")
    print("   ✅ Automatic component restoration")
    print("   ✅ Maintains user selections and page state")
    print("   ✅ Real-time data updates")

def demonstrate_security_features():
    """
    Demonstrate security and access control features
    """
    print("\n🔐 Security Features...")
    
    print("1. Role-Based Access Control:")
    print("   ✅ Only configured access roles can view data")
    print("   ❌ Administrators do NOT have automatic access")
    print("   ✅ All responses are ephemeral (private)")
    
    print("\n2. Selective Tracking:")
    print("   ✅ Only members with tracked roles are monitored")
    print("   ✅ No data collected from untracked members")
    print("   ✅ Configurable role-based filtering")
    
    print("\n3. Data Privacy:")
    print("   ✅ Secure MongoDB storage with indexing")
    print("   ✅ Automatic data cleanup and retention")
    print("   ✅ Comprehensive access logging")

def demonstrate_performance_features():
    """
    Demonstrate performance optimization features
    """
    print("\n⚡ Performance Features...")
    
    print("1. Efficient Caching:")
    print("   - Message counts batched before database writes")
    print("   - 5-minute cache flush intervals")
    print("   - Memory-efficient data structures")
    
    print("2. Database Optimization:")
    print("   - Compound indexes for fast queries")
    print("   - Aggregation pipelines for complex data")
    print("   - Optimized for time-range queries")
    
    print("3. Scalability:")
    print("   - Designed for large servers (1000+ members)")
    print("   - Concurrent tracking of multiple roles")
    print("   - Real-time processing without lag")

async def main():
    """
    Main example function demonstrating the complete system
    """
    print("🚀 Discord Activity Tracker System Example")
    print("=" * 50)
    
    # Setup example
    setup_success = await setup_activity_tracker_example()
    if not setup_success:
        print("❌ Setup failed, skipping other examples")
        return
    
    # Demonstrate features
    await demonstrate_activity_tracking()
    await demonstrate_data_retrieval()
    demonstrate_discord_commands()
    demonstrate_security_features()
    demonstrate_performance_features()
    
    print("\n" + "=" * 50)
    print("✅ Example demonstration complete!")
    print("\nNext Steps:")
    print("1. Configure your tracked roles using /activity_setup")
    print("2. Set access roles for your moderators/admins")
    print("3. Start tracking member activity automatically")
    print("4. Use /activity_search to view member data")
    print("\nFor detailed documentation, see ACTIVITY_TRACKER_DOCUMENTATION.md")

if __name__ == "__main__":
    # Run the example
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Example interrupted by user")
    except Exception as e:
        print(f"\n❌ Example failed with error: {e}")
        import traceback
        traceback.print_exc()
