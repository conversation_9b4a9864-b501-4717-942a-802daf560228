"""
Test script for Activity Tracker Configuration Persistence

This script tests the configuration save/load functionality to ensure
that tracked roles and access roles are properly persisted to MongoDB.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_configuration_persistence():
    """Test the complete configuration persistence workflow"""
    print("🧪 Testing Activity Tracker Configuration Persistence")
    print("=" * 60)
    
    try:
        # Import after setting up the path
        from activity_tracker import activity_tracker
        
        print("1. Initializing Activity Tracker...")
        await activity_tracker.initialize()
        print("✅ Activity tracker initialized")
        
        print("\n2. Testing Configuration Save/Load...")
        
        # Test 1: Save and load empty configuration
        print("   Test 1: Empty configuration")
        activity_tracker.tracked_roles = set()
        activity_tracker.authorized_roles = set()
        
        save_result = await activity_tracker._save_config()
        if save_result:
            print("   ✅ Empty configuration saved successfully")
        else:
            print("   ❌ Failed to save empty configuration")
            return False
        
        # Load and verify
        await activity_tracker._load_config()
        if len(activity_tracker.tracked_roles) == 0 and len(activity_tracker.authorized_roles) == 0:
            print("   ✅ Empty configuration loaded correctly")
        else:
            print("   ❌ Empty configuration not loaded correctly")
            return False
        
        # Test 2: Save and load with data
        print("   Test 2: Configuration with data")
        test_tracked_roles = {123456789, 987654321, 555666777}
        test_access_roles = {111222333, 444555666}
        
        activity_tracker.tracked_roles = test_tracked_roles
        activity_tracker.authorized_roles = test_access_roles
        
        save_result = await activity_tracker._save_config()
        if save_result:
            print("   ✅ Configuration with data saved successfully")
        else:
            print("   ❌ Failed to save configuration with data")
            return False
        
        # Clear and reload
        activity_tracker.tracked_roles = set()
        activity_tracker.authorized_roles = set()
        
        await activity_tracker._load_config()
        
        if (activity_tracker.tracked_roles == test_tracked_roles and 
            activity_tracker.authorized_roles == test_access_roles):
            print("   ✅ Configuration with data loaded correctly")
        else:
            print("   ❌ Configuration with data not loaded correctly")
            print(f"      Expected tracked: {test_tracked_roles}")
            print(f"      Got tracked: {activity_tracker.tracked_roles}")
            print(f"      Expected access: {test_access_roles}")
            print(f"      Got access: {activity_tracker.authorized_roles}")
            return False
        
        print("\n3. Testing Database Direct Access...")
        
        # Test direct database access
        config_doc = await activity_tracker.config_collection.find_one({"_id": "activity_config"})
        if config_doc:
            print("   ✅ Configuration document found in database")
            print(f"      Tracked roles: {config_doc.get('tracked_roles', [])}")
            print(f"      Access roles: {config_doc.get('authorized_roles', [])}")
            print(f"      Last updated: {config_doc.get('last_updated')}")
        else:
            print("   ❌ Configuration document not found in database")
            return False
        
        print("\n4. Testing Built-in Test Method...")
        test_result = await activity_tracker.test_configuration_persistence()
        if test_result:
            print("   ✅ Built-in persistence test passed")
        else:
            print("   ❌ Built-in persistence test failed")
            return False
        
        print("\n5. Testing Configuration Update Workflow...")
        
        # Simulate the modal workflow
        original_tracked = activity_tracker.tracked_roles.copy()
        original_access = activity_tracker.authorized_roles.copy()
        
        # Update configuration
        new_tracked = {999888777, 666555444}
        new_access = {333222111}
        
        activity_tracker.tracked_roles = new_tracked
        activity_tracker.authorized_roles = new_access
        
        save_result = await activity_tracker._save_config()
        if save_result:
            print("   ✅ Configuration update saved")
        else:
            print("   ❌ Configuration update failed to save")
            return False
        
        # Verify persistence by reloading
        activity_tracker.tracked_roles = set()
        activity_tracker.authorized_roles = set()
        await activity_tracker._load_config()
        
        if (activity_tracker.tracked_roles == new_tracked and 
            activity_tracker.authorized_roles == new_access):
            print("   ✅ Updated configuration persisted correctly")
        else:
            print("   ❌ Updated configuration not persisted correctly")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 All configuration persistence tests passed!")
        print("\nThe activity tracker configuration system is working correctly:")
        print("✅ Configuration saves to MongoDB properly")
        print("✅ Configuration loads from MongoDB correctly")
        print("✅ Database document structure is correct")
        print("✅ Update workflow functions properly")
        print("✅ Data persistence survives reload cycles")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_modal_simulation():
    """Simulate the modal submission workflow"""
    print("\n🎭 Testing Modal Simulation Workflow")
    print("-" * 40)
    
    try:
        from activity_tracker import activity_tracker
        
        # Simulate TrackedRolesModal workflow
        print("1. Simulating TrackedRolesModal submission...")
        
        # Test role parsing logic
        role_inputs = ["123456789", "Staff Role", "987654321"]
        new_tracked_roles = set()
        
        for role_input in role_inputs:
            if role_input.isdigit():
                new_tracked_roles.add(int(role_input))
            else:
                # In real scenario, this would look up the role by name
                # For testing, we'll just add a fake ID
                new_tracked_roles.add(hash(role_input) % 1000000)
        
        activity_tracker.tracked_roles = new_tracked_roles
        save_result = await activity_tracker._save_config()
        
        if save_result:
            print("   ✅ Tracked roles modal simulation successful")
        else:
            print("   ❌ Tracked roles modal simulation failed")
            return False
        
        # Simulate AccessRolesModal workflow
        print("2. Simulating AccessRolesModal submission...")
        
        role_inputs = ["444555666", "Admin Role"]
        new_access_roles = set()
        
        for role_input in role_inputs:
            if role_input.isdigit():
                new_access_roles.add(int(role_input))
            else:
                new_access_roles.add(hash(role_input) % 1000000)
        
        activity_tracker.authorized_roles = new_access_roles
        save_result = await activity_tracker._save_config()
        
        if save_result:
            print("   ✅ Access roles modal simulation successful")
        else:
            print("   ❌ Access roles modal simulation failed")
            return False
        
        print("✅ Modal simulation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Modal simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Activity Tracker Configuration Persistence Test Suite")
    print("=" * 70)
    
    # Run configuration persistence tests
    config_test_result = await test_configuration_persistence()
    
    if config_test_result:
        # Run modal simulation tests
        modal_test_result = await test_modal_simulation()
        
        if modal_test_result:
            print("\n" + "=" * 70)
            print("🎉 ALL TESTS PASSED!")
            print("\nThe activity tracker configuration persistence is working correctly.")
            print("You can now use /activity_setup to configure roles and they will persist.")
            return True
    
    print("\n" + "=" * 70)
    print("❌ TESTS FAILED!")
    print("\nPlease check the error messages above and fix the issues.")
    return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
