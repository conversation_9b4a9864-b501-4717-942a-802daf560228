#!/usr/bin/env python3
"""
Test script for the improved /set_application_log_channel command.
This script tests the enhanced command with better parameter names and flexible configuration.
"""

import asyncio
import discord
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

class MockUser:
    def __init__(self, user_id=54321, name="AdminUser", discriminator="0002"):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.display_name = f"{name}#{discriminator}"
        self.guild_permissions = MagicMock()
        self.guild_permissions.administrator = True

class MockChannel:
    def __init__(self, channel_id, name):
        self.id = channel_id
        self.name = name
        self.mention = f"<#{channel_id}>"

class MockInteraction:
    def __init__(self, user):
        self.user = user
        self.response = MagicMock()
        self.response.send_message = AsyncMock()

async def test_log_channel_only():
    """Test setting only the log channel (minimal configuration)"""
    print("=== Test 1: Log Channel Only Configuration ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_log_channel', None), \
         patch('bot.application_response_channel', None), \
         patch('bot.application_response_always_notify', True), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        log_channel = MockChannel(12345, "application-logs")
        mock_bot.get_channel.return_value = None  # No existing response channel
        
        from bot import set_application_log_channel
        
        admin_user = MockUser()
        interaction = MockInteraction(admin_user)
        
        try:
            # Test with only log_channel parameter
            await set_application_log_channel(interaction, log_channel)
            print("✅ Log channel only configuration executed successfully")
            print(f"   Command: /set_application_log_channel log_channel:#{log_channel.name}")
            
            # Verify save was called
            mock_save.assert_called_once()
            print("✅ Configuration saved to database")
            
        except Exception as e:
            print(f"❌ Log channel only configuration failed: {e}")

async def test_log_and_response_channels():
    """Test setting log channel and response channel (without notify_both parameter)"""
    print("\n=== Test 2: Log + Response Channels Configuration ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_log_channel', None), \
         patch('bot.application_response_channel', None), \
         patch('bot.application_response_always_notify', True), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        log_channel = MockChannel(12345, "application-logs")
        response_channel = MockChannel(67890, "unified-notifications")
        mock_bot.get_channel.return_value = response_channel
        
        from bot import set_application_log_channel
        
        admin_user = MockUser()
        interaction = MockInteraction(admin_user)
        
        try:
            # Test with log_channel and response_channel parameters (notify_both defaults to True)
            await set_application_log_channel(interaction, log_channel, response_channel)
            print("✅ Log + Response channels configuration executed successfully")
            print(f"   Command: /set_application_log_channel log_channel:#{log_channel.name} response_channel:#{response_channel.name}")
            print("   notify_both: True (default)")
            
            # Verify save was called
            mock_save.assert_called_once()
            print("✅ Configuration saved to database")
            
        except Exception as e:
            print(f"❌ Log + Response channels configuration failed: {e}")

async def test_full_configuration_notify_both_true():
    """Test full configuration with notify_both=True"""
    print("\n=== Test 3: Full Configuration (notify_both=True) ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_log_channel', None), \
         patch('bot.application_response_channel', None), \
         patch('bot.application_response_always_notify', True), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        log_channel = MockChannel(12345, "application-logs")
        response_channel = MockChannel(67890, "unified-notifications")
        mock_bot.get_channel.return_value = response_channel
        
        from bot import set_application_log_channel
        
        admin_user = MockUser()
        interaction = MockInteraction(admin_user)
        
        try:
            # Test with all parameters, notify_both=True
            await set_application_log_channel(interaction, log_channel, response_channel, notify_both=True)
            print("✅ Full configuration (notify_both=True) executed successfully")
            print(f"   Command: /set_application_log_channel log_channel:#{log_channel.name} response_channel:#{response_channel.name} notify_both:True")
            print("   Behavior: Dual notifications (DM + Channel)")
            
            # Verify save was called
            mock_save.assert_called_once()
            print("✅ Configuration saved to database")
            
        except Exception as e:
            print(f"❌ Full configuration (notify_both=True) failed: {e}")

async def test_full_configuration_notify_both_false():
    """Test full configuration with notify_both=False"""
    print("\n=== Test 4: Full Configuration (notify_both=False) ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_log_channel', None), \
         patch('bot.application_response_channel', None), \
         patch('bot.application_response_always_notify', True), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        log_channel = MockChannel(12345, "application-logs")
        response_channel = MockChannel(67890, "unified-notifications")
        mock_bot.get_channel.return_value = response_channel
        
        from bot import set_application_log_channel
        
        admin_user = MockUser()
        interaction = MockInteraction(admin_user)
        
        try:
            # Test with all parameters, notify_both=False
            await set_application_log_channel(interaction, log_channel, response_channel, notify_both=False)
            print("✅ Full configuration (notify_both=False) executed successfully")
            print(f"   Command: /set_application_log_channel log_channel:#{log_channel.name} response_channel:#{response_channel.name} notify_both:False")
            print("   Behavior: Fallback only (Channel when DM fails)")
            
            # Verify save was called
            mock_save.assert_called_once()
            print("✅ Configuration saved to database")
            
        except Exception as e:
            print(f"❌ Full configuration (notify_both=False) failed: {e}")

async def test_partial_update_existing_config():
    """Test updating only log channel when response channel already exists"""
    print("\n=== Test 5: Partial Update (Existing Response Channel) ===")
    
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_log_channel', 11111), \
         patch('bot.application_response_channel', 67890), \
         patch('bot.application_response_always_notify', False), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        old_log_channel = MockChannel(11111, "old-logs")
        new_log_channel = MockChannel(12345, "new-application-logs")
        existing_response_channel = MockChannel(67890, "existing-notifications")
        
        mock_bot.get_channel.side_effect = lambda cid: {
            11111: old_log_channel,
            12345: new_log_channel,
            67890: existing_response_channel
        }.get(cid)
        
        from bot import set_application_log_channel
        
        admin_user = MockUser()
        interaction = MockInteraction(admin_user)
        
        try:
            # Test updating only log channel, keeping existing response channel config
            await set_application_log_channel(interaction, new_log_channel)
            print("✅ Partial update executed successfully")
            print(f"   Command: /set_application_log_channel log_channel:#{new_log_channel.name}")
            print("   Existing response channel configuration preserved")
            print("   Existing notify_both setting preserved")
            
            # Verify save was called
            mock_save.assert_called_once()
            print("✅ Configuration saved to database")
            
        except Exception as e:
            print(f"❌ Partial update failed: {e}")

async def test_permission_check():
    """Test permission checking"""
    print("\n=== Test 6: Permission Check ===")
    
    with patch('bot.save_data_optimized', AsyncMock()):
        
        log_channel = MockChannel(12345, "application-logs")
        
        from bot import set_application_log_channel
        
        # Test with non-admin user
        non_admin_user = MockUser()
        non_admin_user.guild_permissions.administrator = False
        interaction = MockInteraction(non_admin_user)
        
        try:
            await set_application_log_channel(interaction, log_channel)
            print("❌ Command should have failed for non-admin user")
        except Exception as e:
            print("✅ Permission check working correctly")
            print("   Non-admin users cannot use this command")

async def main():
    """Run all tests"""
    print("🧪 Improved /set_application_log_channel Command Tests")
    print("=" * 70)
    
    await test_log_channel_only()
    await test_log_and_response_channels()
    await test_full_configuration_notify_both_true()
    await test_full_configuration_notify_both_false()
    await test_partial_update_existing_config()
    await test_permission_check()
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print("✅ All tests completed. Check output above for any failures.")
    print("\n💡 Key Improvements Verified:")
    print("1. ✅ Renamed 'always_notify' to 'notify_both' for clarity")
    print("2. ✅ Made response_channel and notify_both parameters optional")
    print("3. ✅ Flexible configuration options (log only, log+response, full config)")
    print("4. ✅ Partial updates preserve existing configuration")
    print("5. ✅ Clear parameter descriptions and usage examples")
    print("6. ✅ Enhanced confirmation embed with current status")
    print("\n🔧 Usage Examples:")
    print("• Log only: /set_application_log_channel log_channel:#logs")
    print("• Log + Response: /set_application_log_channel log_channel:#logs response_channel:#notifications")
    print("• Full config (dual): /set_application_log_channel log_channel:#logs response_channel:#notifications notify_both:True")
    print("• Full config (fallback): /set_application_log_channel log_channel:#logs response_channel:#notifications notify_both:False")

if __name__ == "__main__":
    asyncio.run(main())
